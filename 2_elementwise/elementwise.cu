#include <algorithm>
#include <cuda_bf16.h>
#include <cuda_fp16.h>
#include <cuda_fp8.h>
#include <cuda_runtime.h>
#include <float.h>
#include <stdio.h>
#include <stdlib.h>
#include <vector>

#define WARP_SIZE 32
#define INT4(value) (reinterpret_cast<int4 *>(&(value))[0])
#define FLOAT4(value) (reinterpret_cast<float4 *>(&(value))[0])
#define HALF2(value) (reinterpret_cast<half2 *>(&(value))[0])
#define BFLOAT2(value) (reinterpret_cast<__nv_bfloat162 *>(&(value))[0])
#define LDST128BITS(value) (reinterpret_cast<float4 *>(&(value))[0])

// FP32
// ElementWise Add grid(N/256),
// block(256) a: Nx1, b: Nx1, c: Nx1, c = elementwise_add(a, b)
__global__ void elementwise_add_f32_kernel(float *a, float *b, float *c,
                                           int N) {
  int idx = blockIdx.x * blockDim.x + threadIdx.x;
  if (idx < N)
    c[idx] = a[idx] + b[idx];
}

// ElementWise Add + Vec4
// grid(N/256), block(256/4)
// a: Nx1, b: Nx1, c: Nx1, c = elementwise_add(a, b)
__global__ void elementwise_add_f32x4_kernel(float *a, float *b, float *c,
                                             int N) {
  int idx = 4 * (blockIdx.x * blockDim.x + threadIdx.x);
  if (idx < N) {
    float4 reg_a = FLOAT4(a[idx]);
    float4 reg_b = FLOAT4(b[idx]);
    float4 reg_c;
    reg_c.x = reg_a.x + reg_b.x;
    reg_c.y = reg_a.y + reg_b.y;
    reg_c.z = reg_a.z + reg_b.z;
    reg_c.w = reg_a.w + reg_b.w;
    FLOAT4(c[idx]) = reg_c;
  }
}

// FP16
// ElementWise Add grid(N/256),
// block(256) a: Nx1, b: Nx1, c: Nx1, c = elementwise_add(a, b)
__global__ void elementwise_add_f16_kernel(half *a, half *b, half *c, int N) {
  int idx = blockIdx.x * blockDim.x + threadIdx.x;
  if (idx < N)
    c[idx] = __hadd(a[idx], b[idx]);
}

// a: Nx1, b: Nx1, c: Nx1, c = elementwise_add(a, b)
__global__ void elementwise_add_f16x2_kernel(half *a, half *b, half *c, int N) {
  int idx = 2 * (blockIdx.x * blockDim.x + threadIdx.x);
  if (idx < N) {
    half2 reg_a = HALF2(a[idx]);
    half2 reg_b = HALF2(b[idx]);
    half2 reg_c;
    reg_c.x = __hadd(reg_a.x, reg_b.x);
    reg_c.y = __hadd(reg_a.y, reg_b.y);
    HALF2(c[idx]) = reg_c;
  }
}

__global__ void elementwise_add_f16x8_kernel(half *a, half *b, half *c, int N) {
  int idx = 8 * (blockIdx.x * blockDim.x + threadIdx.x);
  half2 reg_a_0 = HALF2(a[idx + 0]);
  half2 reg_a_1 = HALF2(a[idx + 2]);
  half2 reg_a_2 = HALF2(a[idx + 4]);
  half2 reg_a_3 = HALF2(a[idx + 6]);
  half2 reg_b_0 = HALF2(b[idx + 0]);
  half2 reg_b_1 = HALF2(b[idx + 2]);
  half2 reg_b_2 = HALF2(b[idx + 4]);
  half2 reg_b_3 = HALF2(b[idx + 6]);
  half2 reg_c_0, reg_c_1, reg_c_2, reg_c_3;
  reg_c_0.x = __hadd(reg_a_0.x, reg_b_0.x);
  reg_c_0.y = __hadd(reg_a_0.y, reg_b_0.y);
  reg_c_1.x = __hadd(reg_a_1.x, reg_b_1.x);
  reg_c_1.y = __hadd(reg_a_1.y, reg_b_1.y);
  reg_c_2.x = __hadd(reg_a_2.x, reg_b_2.x);
  reg_c_2.y = __hadd(reg_a_2.y, reg_b_2.y);
  reg_c_3.x = __hadd(reg_a_3.x, reg_b_3.x);
  reg_c_3.y = __hadd(reg_a_3.y, reg_b_3.y);
  if ((idx + 0) < N) {
    HALF2(c[idx + 0]) = reg_c_0;
  }
  if ((idx + 2) < N) {
    HALF2(c[idx + 2]) = reg_c_1;
  }
  if ((idx + 4) < N) {
    HALF2(c[idx + 4]) = reg_c_2;
  }
  if ((idx + 6) < N) {
    HALF2(c[idx + 6]) = reg_c_3;
  }
}

__global__ void elementwise_add_f16x8_pack_kernel(half *a, half *b, half *c,
                                                  int N) {
  int idx = 8 * (blockIdx.x * blockDim.x + threadIdx.x);
  // temporary register(memory), .local space in ptx, addressable
  half pack_a[8], pack_b[8], pack_c[8]; // 8x16 bits=128 bits.
  // reinterpret as float4 and load 128 bits in 1 memory issue.
  LDST128BITS(pack_a[0]) = LDST128BITS(a[idx]); // load 128 bits
  LDST128BITS(pack_b[0]) = LDST128BITS(b[idx]); // load 128 bits

#pragma unroll
  for (int i = 0; i < 8; i += 2) {
    // __hadd2 for half2 x 4
    HALF2(pack_c[i]) = __hadd2(HALF2(pack_a[i]), HALF2(pack_b[i]));
  }
  // reinterpret as float4 and store 128 bits in 1 memory issue.
  if ((idx + 7) < N) {
    LDST128BITS(c[idx]) = LDST128BITS(pack_c[0]);
  }
}

// Helper function to check CUDA errors
#define CHECK_CUDA(call) \
  do { \
    cudaError_t error = call; \
    if (error != cudaSuccess) { \
      printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(error)); \
      exit(1); \
    } \
  } while(0)

// Test function for FP32 kernels
void test_f32_kernels(int N) {
  printf("Testing FP32 kernels with N=%d\n", N);

  // Host memory allocation
  std::vector<float> h_a(N), h_b(N), h_c(N), h_ref(N);

  // Initialize input data
  for (int i = 0; i < N; i++) {
    h_a[i] = static_cast<float>(i % 100) / 10.0f;
    h_b[i] = static_cast<float>((i + 50) % 100) / 10.0f;
    h_ref[i] = h_a[i] + h_b[i]; // Reference result
  }

  // Device memory allocation
  float *d_a, *d_b, *d_c;
  CHECK_CUDA(cudaMalloc(&d_a, N * sizeof(float)));
  CHECK_CUDA(cudaMalloc(&d_b, N * sizeof(float)));
  CHECK_CUDA(cudaMalloc(&d_c, N * sizeof(float)));

  // Copy data to device
  CHECK_CUDA(cudaMemcpy(d_a, h_a.data(), N * sizeof(float), cudaMemcpyHostToDevice));
  CHECK_CUDA(cudaMemcpy(d_b, h_b.data(), N * sizeof(float), cudaMemcpyHostToDevice));

  // Test basic FP32 kernel
  dim3 block(256);
  dim3 grid((N + block.x - 1) / block.x);

  elementwise_add_f32_kernel<<<grid, block>>>(d_a, d_b, d_c, N);
  CHECK_CUDA(cudaGetLastError());
  CHECK_CUDA(cudaDeviceSynchronize());

  // Copy result back and verify
  CHECK_CUDA(cudaMemcpy(h_c.data(), d_c, N * sizeof(float), cudaMemcpyDeviceToHost));

  bool correct = true;
  for (int i = 0; i < N; i++) {
    if (abs(h_c[i] - h_ref[i]) > 1e-5) {
      correct = false;
      break;
    }
  }
  printf("FP32 basic kernel: %s\n", correct ? "PASSED" : "FAILED");

  // Test FP32x4 vectorized kernel
  if (N % 4 == 0) {
    dim3 block_vec(64); // 256/4 = 64
    dim3 grid_vec((N/4 + block_vec.x - 1) / block_vec.x);

    elementwise_add_f32x4_kernel<<<grid_vec, block_vec>>>(d_a, d_b, d_c, N);
    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaMemcpy(h_c.data(), d_c, N * sizeof(float), cudaMemcpyDeviceToHost));

    correct = true;
    for (int i = 0; i < N; i++) {
      if (abs(h_c[i] - h_ref[i]) > 1e-5) {
        correct = false;
        break;
      }
    }
    printf("FP32x4 vectorized kernel: %s\n", correct ? "PASSED" : "FAILED");
  }

  // Cleanup
  CHECK_CUDA(cudaFree(d_a));
  CHECK_CUDA(cudaFree(d_b));
  CHECK_CUDA(cudaFree(d_c));
}

// Test function for FP16 kernels
void test_f16_kernels(int N) {
  printf("Testing FP16 kernels with N=%d\n", N);

  // Host memory allocation
  std::vector<half> h_a(N), h_b(N), h_c(N);
  std::vector<float> h_ref(N);

  // Initialize input data
  for (int i = 0; i < N; i++) {
    float val_a = static_cast<float>(i % 100) / 10.0f;
    float val_b = static_cast<float>((i + 50) % 100) / 10.0f;
    h_a[i] = __float2half(val_a);
    h_b[i] = __float2half(val_b);
    h_ref[i] = val_a + val_b; // Reference result in float
  }

  // Device memory allocation
  half *d_a, *d_b, *d_c;
  CHECK_CUDA(cudaMalloc(&d_a, N * sizeof(half)));
  CHECK_CUDA(cudaMalloc(&d_b, N * sizeof(half)));
  CHECK_CUDA(cudaMalloc(&d_c, N * sizeof(half)));

  // Copy data to device
  CHECK_CUDA(cudaMemcpy(d_a, h_a.data(), N * sizeof(half), cudaMemcpyHostToDevice));
  CHECK_CUDA(cudaMemcpy(d_b, h_b.data(), N * sizeof(half), cudaMemcpyHostToDevice));

  // Test basic FP16 kernel
  dim3 block(256);
  dim3 grid((N + block.x - 1) / block.x);

  elementwise_add_f16_kernel<<<grid, block>>>(d_a, d_b, d_c, N);
  CHECK_CUDA(cudaGetLastError());
  CHECK_CUDA(cudaDeviceSynchronize());

  // Copy result back and verify
  CHECK_CUDA(cudaMemcpy(h_c.data(), d_c, N * sizeof(half), cudaMemcpyDeviceToHost));

  bool correct = true;
  for (int i = 0; i < N; i++) {
    float result = __half2float(h_c[i]);
    if (abs(result - h_ref[i]) > 1e-2) { // Looser tolerance for FP16
      correct = false;
      break;
    }
  }
  printf("FP16 basic kernel: %s\n", correct ? "PASSED" : "FAILED");

  // Test FP16x2 kernel
  if (N % 2 == 0) {
    dim3 block_vec(128); // 256/2 = 128
    dim3 grid_vec((N/2 + block_vec.x - 1) / block_vec.x);

    elementwise_add_f16x2_kernel<<<grid_vec, block_vec>>>(d_a, d_b, d_c, N);
    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaMemcpy(h_c.data(), d_c, N * sizeof(half), cudaMemcpyDeviceToHost));

    correct = true;
    for (int i = 0; i < N; i++) {
      float result = __half2float(h_c[i]);
      if (abs(result - h_ref[i]) > 1e-2) {
        correct = false;
        break;
      }
    }
    printf("FP16x2 vectorized kernel: %s\n", correct ? "PASSED" : "FAILED");
  }

  // Cleanup
  CHECK_CUDA(cudaFree(d_a));
  CHECK_CUDA(cudaFree(d_b));
  CHECK_CUDA(cudaFree(d_c));
}

int main() {
  printf("CUDA Elementwise Addition Kernels Test\n");
  printf("======================================\n");

  // Check CUDA device
  int deviceCount;
  CHECK_CUDA(cudaGetDeviceCount(&deviceCount));
  if (deviceCount == 0) {
    printf("No CUDA devices found!\n");
    return 1;
  }

  cudaDeviceProp prop;
  CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
  printf("Using device: %s\n", prop.name);
  printf("Compute capability: %d.%d\n", prop.major, prop.minor);
  printf("\n");

  // Test with different sizes
  std::vector<int> test_sizes = {1024, 4096, 16384, 65536};

  for (int N : test_sizes) {
    printf("----------------------------------------\n");
    test_f32_kernels(N);
    printf("\n");
    test_f16_kernels(N);
    printf("\n");
  }

  printf("All tests completed!\n");
  return 0;
}