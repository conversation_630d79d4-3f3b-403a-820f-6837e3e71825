add_executable(elementwise_add elementwise_add.cu)
target_link_libraries(elementwise_add PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(elementwise_add PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(elementwise_add PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

# Add executable for elementwise.cu
add_executable(elementwise elementwise.cu)
target_link_libraries(elementwise PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(elementwise PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(elementwise PROPERTIES CUDA_SEPARABLE_COMPILATION ON)