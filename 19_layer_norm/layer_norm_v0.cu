
#include <algorithm>
#include <cuda_runtime.h>
#include <stdio.h>
#include <stdlib.h>
#include <vector>
#include <chrono>
#include <cmath>
#include <iostream>

#define WARP_SIZE 32

#define checkCudaErrors(func)                                                  \
  {                                                                            \
    cudaError_t e = (func);                                                    \
    if (e != cudaSuccess)                                                      \
      printf("%s %d CUDA: %s\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
  }

// Warp reduce sum function
template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ float warp_reduce_sum(float val) {
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val += __shfl_xor_sync(0xffffffff, val, mask);
  }
  return val;
}

// Block reduce sum function
template <const int NUM_THREADS>
__device__ __forceinline__ float block_reduce_sum(float val) {
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];

  int warp = threadIdx.x / WARP_SIZE;
  int lane = threadIdx.x % WARP_SIZE;

  val = warp_reduce_sum<WARP_SIZE>(val);
  if (lane == 0) reduce_smem[warp] = val;
  __syncthreads();

  val = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  val = warp_reduce_sum<NUM_WARPS>(val);

  return val;
}

// Layer Norm: x: NxK(K=128<1024), y': NxK, y'=x-mean(x)/std(x) each row
// mean(x) = sum(x)/K, 1/std(x) = rsqrtf( sum( (x-mean(x))^2 )/K ) each row
// grid(N), block(K<1024) N=batch_size*seq_len, K=hidden_size
// y=y'*g + b (g: scale, b: bias)
template<const int NUM_THREADS=128>
__global__ void layer_norm(float* x, float* y, float g, float b, int N, int K) {
  int tid = threadIdx.x; // 0..K-1
  int bid = blockIdx.x; // 0..N-1
  int idx = bid * K + tid;
  const float epsilon = 1e-5f;

  __shared__ float s_mean; // shared within block
  __shared__ float s_variance; // shared within block

  float value = (tid < K) ? x[idx] : 0.0f; // load once only
  float sum = block_reduce_sum<NUM_THREADS>(value);
  if (tid == 0) s_mean = sum / (float) K;
  // wait for s_mean in shared memory to be ready for all threads
  __syncthreads();

  float variance = (value - s_mean) * (value - s_mean);
  variance = block_reduce_sum<NUM_THREADS>(variance);
  if (tid == 0) s_variance = rsqrtf(variance / (float) K + epsilon);
  // wait for s_variance in shared memory to be ready for all threads
  __syncthreads();

  if (tid < K) y[idx] = ((value - s_mean) * s_variance) * g + b;
}

// CPU reference implementation
void layer_norm_cpu(const float* x, float* y, float g, float b, int N, int K) {
  const float epsilon = 1e-5f;

  for (int n = 0; n < N; n++) {
    const float* x_row = x + n * K;
    float* y_row = y + n * K;

    // Compute mean
    float mean = 0.0f;
    for (int k = 0; k < K; k++) {
      mean += x_row[k];
    }
    mean /= K;

    // Compute variance
    float variance = 0.0f;
    for (int k = 0; k < K; k++) {
      float diff = x_row[k] - mean;
      variance += diff * diff;
    }
    variance /= K;

    // Compute output
    float inv_std = 1.0f / sqrtf(variance + epsilon);
    for (int k = 0; k < K; k++) {
      y_row[k] = ((x_row[k] - mean) * inv_std) * g + b;
    }
  }
}

// Initialize matrix with test data
void init_matrix(float* x, int N, int K) {
  for (int i = 0; i < N * K; i++) {
    x[i] = static_cast<float>(rand()) / RAND_MAX * 4.0f - 2.0f; // Range [-2, 2]
  }
}

// Verify results
bool verify_result(const float* gpu_result, const float* cpu_result, int N, int K, float tolerance = 1e-4f) {
  int failed_count = 0;
  for (int i = 0; i < N * K; i++) {
    float diff = std::abs(gpu_result[i] - cpu_result[i]);
    if (diff > tolerance) {
      if (failed_count < 5) { // Only print first 5 failures
        printf("Verification failed at index %d: GPU=%.6f, CPU=%.6f, diff=%.6e\n",
               i, gpu_result[i], cpu_result[i], diff);
      }
      failed_count++;
    }
  }

  if (failed_count > 0) {
    printf("Total failed elements: %d/%d\n", failed_count, N * K);
    return false;
  }
  return true;
}

// Performance testing function
template<typename KernelFunc>
double benchmark_kernel(KernelFunc kernel_func, float* d_x, float* d_y, float g, float b,
                       int N, int K, int num_iterations = 1000) {
  // Warm up
  for (int i = 0; i < 10; i++) {
    kernel_func(d_x, d_y, g, b, N, K);
  }
  checkCudaErrors(cudaDeviceSynchronize());

  // Timing
  auto start = std::chrono::high_resolution_clock::now();
  for (int i = 0; i < num_iterations; i++) {
    kernel_func(d_x, d_y, g, b, N, K);
  }
  checkCudaErrors(cudaDeviceSynchronize());
  auto end = std::chrono::high_resolution_clock::now();

  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
  return duration.count() / 1000.0 / num_iterations; // ms per iteration
}

int main(int argc, char **argv) {
  // Parse command line arguments
  int N = 1024;  // batch_size * seq_len
  int K = 128;   // hidden_size

  if (argc >= 3) {
    N = atoi(argv[1]);
    K = atoi(argv[2]);
  }

  printf("Layer Norm v0 Performance Test\n");
  printf("Matrix size: %d x %d\n", N, K);
  printf("=================================\n");

  // Allocate host memory
  size_t size = N * K * sizeof(float);
  float *h_x = (float*)malloc(size);
  float *h_y_gpu = (float*)malloc(size);
  float *h_y_cpu = (float*)malloc(size);

  // Initialize input matrix
  srand(42); // For reproducible results
  init_matrix(h_x, N, K);

  // Layer norm parameters
  float g = 1.0f; // scale
  float b = 0.0f; // bias

  // Allocate device memory
  float *d_x, *d_y;
  checkCudaErrors(cudaMalloc(&d_x, size));
  checkCudaErrors(cudaMalloc(&d_y, size));

  // Copy input data to device
  checkCudaErrors(cudaMemcpy(d_x, h_x, size, cudaMemcpyHostToDevice));

  // Compute CPU reference
  printf("Computing CPU reference...\n");
  auto cpu_start = std::chrono::high_resolution_clock::now();
  layer_norm_cpu(h_x, h_y_cpu, g, b, N, K);
  auto cpu_end = std::chrono::high_resolution_clock::now();
  auto cpu_time = std::chrono::duration_cast<std::chrono::microseconds>(cpu_end - cpu_start);
  printf("CPU time: %.3f ms\n\n", cpu_time.count() / 1000.0);

  // Test GPU kernel
  printf("Testing GPU Layer Norm kernel...\n");
  constexpr int NUM_THREADS = 128;
  dim3 block(NUM_THREADS);
  dim3 grid(N);

  layer_norm<NUM_THREADS><<<grid, block>>>(d_x, d_y, g, b, N, K);
  checkCudaErrors(cudaGetLastError());
  checkCudaErrors(cudaDeviceSynchronize());

  checkCudaErrors(cudaMemcpy(h_y_gpu, d_y, size, cudaMemcpyDeviceToHost));

  // Verify result
  bool passed = verify_result(h_y_gpu, h_y_cpu, N, K);
  printf("Verification: %s\n", passed ? "PASSED" : "FAILED");

  if (passed) {
    // Benchmark performance
    double time_ms = benchmark_kernel(
        [&](float *x, float *y, float g_val, float b_val, int n, int k) {
          layer_norm<NUM_THREADS><<<grid, block>>>(x, y, g_val, b_val, n, k);
        },
        d_x, d_y, g, b, N, K);

    double bandwidth = (2.0 * size) / (time_ms * 1e-3) / 1e9; // GB/s (read + write)
    printf("Performance: %.3f ms, %.2f GB/s\n", time_ms, bandwidth);
  }

  printf("\n=================================\n");
  printf("Overall result: %s\n", passed ? "PASSED" : "FAILED");

  // Cleanup
  free(h_x);
  free(h_y_gpu);
  free(h_y_cpu);
  checkCudaErrors(cudaFree(d_x));
  checkCudaErrors(cudaFree(d_y));

  return passed ? 0 : 1;
}