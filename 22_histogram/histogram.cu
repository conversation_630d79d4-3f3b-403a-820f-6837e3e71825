#include <algorithm>
#include <cuda_runtime.h>
#include <float.h>
#include <stdio.h>
#include <stdlib.h>
#include <tuple>
#include <vector>

#define WARP_SIZE 32
#define INT4(value) (reinterpret_cast<int4 *>(&(value))[0])
#define FLOAT4(value) (reinterpret_cast<float4 *>(&(value))[0])

// Histogram
// grid(N/256), block(256)
// a: Nx1, y: count histogram, a >= 1
__global__ void histogram_i32_kernel(int *a, int *y, int N) {
  int idx = blockIdx.x * blockDim.x + threadIdx.x;
  if (idx < N)
    atomicAdd(&(y[a[idx]]), 1);
}

// Histogram + Vec4
// grid(N/256), block(256/4)
// a: Nx1, y: count histogram, a >= 1
__global__ void histogram_i32x4_kernel(int *a, int *y, int N) {
  int idx = 4 * (blockIdx.x * blockDim.x + threadIdx.x);
  if (idx < N) {
    int4 reg_a = INT4(a[idx]);
    atomicAdd(&(y[reg_a.x]), 1);
    atomicAdd(&(y[reg_a.y]), 1);
    atomicAdd(&(y[reg_a.z]), 1);
    atomicAdd(&(y[reg_a.w]), 1);
  }
}

// Helper function to check CUDA errors
#define CHECK_CUDA(call) \
  do { \
    cudaError_t error = call; \
    if (error != cudaSuccess) { \
      printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(error)); \
      exit(1); \
    } \
  } while(0)

// CPU reference implementation for verification
void histogram_cpu_reference(const std::vector<int>& input, std::vector<int>& histogram, int num_bins) {
  std::fill(histogram.begin(), histogram.end(), 0);
  for (int val : input) {
    if (val >= 0 && val < num_bins) {
      histogram[val]++;
    }
  }
}

// Test function for basic histogram kernel
void test_histogram_basic(const std::vector<int>& h_input, int num_bins, const std::vector<int>& h_ref) {
  int N = h_input.size();
  printf("Testing basic histogram kernel with N=%d, bins=%d\n", N, num_bins);

  // Device memory allocation
  int *d_input, *d_histogram;
  CHECK_CUDA(cudaMalloc(&d_input, N * sizeof(int)));
  CHECK_CUDA(cudaMalloc(&d_histogram, num_bins * sizeof(int)));

  // Copy input data to device and initialize histogram to zero
  CHECK_CUDA(cudaMemcpy(d_input, h_input.data(), N * sizeof(int), cudaMemcpyHostToDevice));
  CHECK_CUDA(cudaMemset(d_histogram, 0, num_bins * sizeof(int)));

  // Launch kernel
  dim3 block(256);
  dim3 grid((N + block.x - 1) / block.x);

  // Timing
  cudaEvent_t start, stop;
  CHECK_CUDA(cudaEventCreate(&start));
  CHECK_CUDA(cudaEventCreate(&stop));

  CHECK_CUDA(cudaEventRecord(start));
  histogram_i32_kernel<<<grid, block>>>(d_input, d_histogram, N);
  CHECK_CUDA(cudaEventRecord(stop));

  CHECK_CUDA(cudaGetLastError());
  CHECK_CUDA(cudaDeviceSynchronize());

  float milliseconds = 0;
  CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));

  // Copy result back
  std::vector<int> h_result(num_bins);
  CHECK_CUDA(cudaMemcpy(h_result.data(), d_histogram, num_bins * sizeof(int), cudaMemcpyDeviceToHost));

  // Verify result
  bool correct = true;
  for (int i = 0; i < num_bins; i++) {
    if (h_result[i] != h_ref[i]) {
      correct = false;
      printf("Mismatch at bin %d: got %d, expected %d\n", i, h_result[i], h_ref[i]);
      break;
    }
  }

  printf("Basic histogram kernel: %s (%.3f ms)\n", correct ? "PASSED" : "FAILED", milliseconds);

  // Print first few bins for verification
  printf("First 10 bins: ");
  for (int i = 0; i < std::min(10, num_bins); i++) {
    printf("%d ", h_result[i]);
  }
  printf("\n");

  // Cleanup
  CHECK_CUDA(cudaFree(d_input));
  CHECK_CUDA(cudaFree(d_histogram));
  CHECK_CUDA(cudaEventDestroy(start));
  CHECK_CUDA(cudaEventDestroy(stop));
}

// Test function for vectorized histogram kernel
void test_histogram_vectorized(const std::vector<int>& h_input, int num_bins, const std::vector<int>& h_ref) {
  int N = h_input.size();
  printf("Testing vectorized histogram kernel with N=%d, bins=%d\n", N, num_bins);

  // Check if N is divisible by 4 for vectorized kernel
  if (N % 4 != 0) {
    printf("Skipping vectorized test: N=%d is not divisible by 4\n", N);
    return;
  }

  // Device memory allocation
  int *d_input, *d_histogram;
  CHECK_CUDA(cudaMalloc(&d_input, N * sizeof(int)));
  CHECK_CUDA(cudaMalloc(&d_histogram, num_bins * sizeof(int)));

  // Copy input data to device and initialize histogram to zero
  CHECK_CUDA(cudaMemcpy(d_input, h_input.data(), N * sizeof(int), cudaMemcpyHostToDevice));
  CHECK_CUDA(cudaMemset(d_histogram, 0, num_bins * sizeof(int)));

  // Launch kernel
  dim3 block(64); // 256/4 = 64
  dim3 grid((N/4 + block.x - 1) / block.x);

  // Timing
  cudaEvent_t start, stop;
  CHECK_CUDA(cudaEventCreate(&start));
  CHECK_CUDA(cudaEventCreate(&stop));

  CHECK_CUDA(cudaEventRecord(start));
  histogram_i32x4_kernel<<<grid, block>>>(d_input, d_histogram, N);
  CHECK_CUDA(cudaEventRecord(stop));

  CHECK_CUDA(cudaGetLastError());
  CHECK_CUDA(cudaDeviceSynchronize());

  float milliseconds = 0;
  CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));

  // Copy result back
  std::vector<int> h_result(num_bins);
  CHECK_CUDA(cudaMemcpy(h_result.data(), d_histogram, num_bins * sizeof(int), cudaMemcpyDeviceToHost));

  // Verify result
  bool correct = true;
  for (int i = 0; i < num_bins; i++) {
    if (h_result[i] != h_ref[i]) {
      correct = false;
      printf("Mismatch at bin %d: got %d, expected %d\n", i, h_result[i], h_ref[i]);
      break;
    }
  }

  printf("Vectorized histogram kernel: %s (%.3f ms)\n", correct ? "PASSED" : "FAILED", milliseconds);

  // Print first few bins for verification
  printf("First 10 bins: ");
  for (int i = 0; i < std::min(10, num_bins); i++) {
    printf("%d ", h_result[i]);
  }
  printf("\n");

  // Cleanup
  CHECK_CUDA(cudaFree(d_input));
  CHECK_CUDA(cudaFree(d_histogram));
  CHECK_CUDA(cudaEventDestroy(start));
  CHECK_CUDA(cudaEventDestroy(stop));
}

int main(int argc, char* argv[]) {
  printf("CUDA Histogram Computation Test\n");
  printf("===============================\n");

  // Check CUDA device
  int deviceCount;
  CHECK_CUDA(cudaGetDeviceCount(&deviceCount));
  if (deviceCount == 0) {
    printf("No CUDA devices found!\n");
    return 1;
  }

  cudaDeviceProp prop;
  CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
  printf("Using device: %s\n", prop.name);
  printf("Compute capability: %d.%d\n", prop.major, prop.minor);
  printf("\n");

  // Parse command line arguments
  int N = 65536;  // Default number of elements
  int num_bins = 256;  // Default number of histogram bins

  if (argc > 1) {
    N = atoi(argv[1]);
    if (N <= 0) {
      printf("Invalid number of elements: %s\n", argv[1]);
      return 1;
    }
  }

  if (argc > 2) {
    num_bins = atoi(argv[2]);
    if (num_bins <= 0) {
      printf("Invalid number of bins: %s\n", argv[2]);
      return 1;
    }
  }

  printf("Configuration:\n");
  printf("  Number of elements: %d\n", N);
  printf("  Number of bins: %d\n", num_bins);
  printf("  Usage: %s [num_elements] [num_bins]\n\n", argv[0]);

  // Generate test data
  printf("Generating test data...\n");
  std::vector<int> h_input(N);

  // Initialize with random values in range [0, num_bins)
  srand(42); // Fixed seed for reproducible results
  for (int i = 0; i < N; i++) {
    h_input[i] = rand() % num_bins;
  }

  // Add some specific patterns for testing
  if (N >= 1000) {
    // Add some concentrated values for testing
    for (int i = 0; i < 100; i++) {
      h_input[i] = 0;  // Many zeros
      h_input[i + 100] = 1;  // Many ones
      h_input[i + 200] = num_bins - 1;  // Many max values
    }
  }

  printf("Sample input values: ");
  for (int i = 0; i < std::min(20, N); i++) {
    printf("%d ", h_input[i]);
  }
  printf("...\n\n");

  // Compute reference result on CPU
  printf("Computing reference histogram on CPU...\n");
  std::vector<int> h_ref(num_bins);
  histogram_cpu_reference(h_input, h_ref, num_bins);

  printf("Reference histogram (first 10 bins): ");
  for (int i = 0; i < std::min(10, num_bins); i++) {
    printf("%d ", h_ref[i]);
  }
  printf("\n");

  // Calculate total count for verification
  int total_count = 0;
  for (int count : h_ref) {
    total_count += count;
  }
  printf("Total count: %d (should equal N=%d)\n\n", total_count, N);

  // Test different kernel implementations
  printf("Testing CUDA histogram kernels:\n");
  printf("================================\n");

  test_histogram_basic(h_input, num_bins, h_ref);
  printf("\n");

  test_histogram_vectorized(h_input, num_bins, h_ref);
  printf("\n");

  // Performance comparison with multiple runs
  printf("Performance comparison (10 runs average):\n");
  printf("=========================================\n");

  const int num_runs = 10;
  float total_time_basic = 0.0f;
  float total_time_vectorized = 0.0f;

  // Basic kernel timing
  {
    int *d_input, *d_histogram;
    CHECK_CUDA(cudaMalloc(&d_input, N * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_histogram, num_bins * sizeof(int)));
    CHECK_CUDA(cudaMemcpy(d_input, h_input.data(), N * sizeof(int), cudaMemcpyHostToDevice));

    dim3 block(256);
    dim3 grid((N + block.x - 1) / block.x);

    cudaEvent_t start, stop;
    CHECK_CUDA(cudaEventCreate(&start));
    CHECK_CUDA(cudaEventCreate(&stop));

    for (int run = 0; run < num_runs; run++) {
      CHECK_CUDA(cudaMemset(d_histogram, 0, num_bins * sizeof(int)));
      CHECK_CUDA(cudaEventRecord(start));
      histogram_i32_kernel<<<grid, block>>>(d_input, d_histogram, N);
      CHECK_CUDA(cudaEventRecord(stop));
      CHECK_CUDA(cudaDeviceSynchronize());

      float milliseconds;
      CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
      total_time_basic += milliseconds;
    }

    CHECK_CUDA(cudaFree(d_input));
    CHECK_CUDA(cudaFree(d_histogram));
    CHECK_CUDA(cudaEventDestroy(start));
    CHECK_CUDA(cudaEventDestroy(stop));
  }

  // Vectorized kernel timing (if applicable)
  if (N % 4 == 0) {
    int *d_input, *d_histogram;
    CHECK_CUDA(cudaMalloc(&d_input, N * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_histogram, num_bins * sizeof(int)));
    CHECK_CUDA(cudaMemcpy(d_input, h_input.data(), N * sizeof(int), cudaMemcpyHostToDevice));

    dim3 block(64);
    dim3 grid((N/4 + block.x - 1) / block.x);

    cudaEvent_t start, stop;
    CHECK_CUDA(cudaEventCreate(&start));
    CHECK_CUDA(cudaEventCreate(&stop));

    for (int run = 0; run < num_runs; run++) {
      CHECK_CUDA(cudaMemset(d_histogram, 0, num_bins * sizeof(int)));
      CHECK_CUDA(cudaEventRecord(start));
      histogram_i32x4_kernel<<<grid, block>>>(d_input, d_histogram, N);
      CHECK_CUDA(cudaEventRecord(stop));
      CHECK_CUDA(cudaDeviceSynchronize());

      float milliseconds;
      CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
      total_time_vectorized += milliseconds;
    }

    CHECK_CUDA(cudaFree(d_input));
    CHECK_CUDA(cudaFree(d_histogram));
    CHECK_CUDA(cudaEventDestroy(start));
    CHECK_CUDA(cudaEventDestroy(stop));
  }

  float avg_time_basic = total_time_basic / num_runs;
  printf("Basic kernel average time: %.3f ms\n", avg_time_basic);

  if (N % 4 == 0) {
    float avg_time_vectorized = total_time_vectorized / num_runs;
    printf("Vectorized kernel average time: %.3f ms\n", avg_time_vectorized);
    printf("Speedup: %.2fx\n", avg_time_basic / avg_time_vectorized);
  } else {
    printf("Vectorized kernel: N/A (N not divisible by 4)\n");
  }

  printf("\nAll tests completed!\n");
  return 0;
}