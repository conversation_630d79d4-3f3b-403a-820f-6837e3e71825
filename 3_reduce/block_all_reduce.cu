#include <algorithm>
#include <cuda_bf16.h>
#include <cuda_fp16.h>
#include <cuda_fp8.h>
#include <cuda_runtime.h>
#include <float.h>
#include <stdio.h>
#include <stdlib.h>
#include <vector>

#define WARP_SIZE 32
#define INT4(value) (reinterpret_cast<int4 *>(&(value))[0])
#define FLOAT4(value) (reinterpret_cast<float4 *>(&(value))[0])
#define HALF2(value) (reinterpret_cast<half2 *>(&(value))[0])
#define BFLOAT2(value) (reinterpret_cast<__nv_bfloat162 *>(&(value))[0])
#define LDST128BITS(value) (reinterpret_cast<float4 *>(&(value))[0])

// FP32
// Warp Reduce Sum
template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ float warp_reduce_sum_f32(float val) {
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val += __shfl_xor_sync(0xffffffff, val, mask);
  }
  return val;
}

// Block All Reduce Sum
// grid(N/256), block(256)
// a: Nx1, y=sum(a)
template <const int NUM_THREADS = 256>
__global__ void block_all_reduce_sum_f32_f32_kernel(float *a, float *y, int N) {
  int tid = threadIdx.x;
  int idx = blockIdx.x * NUM_THREADS + tid;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];
  // keep the data in register is enough for warp operaion.
  float sum = (idx < N) ? a[idx] : 0.0f;
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum = warp_reduce_sum_f32<WARP_SIZE>(sum);
  // warp leaders store the data to shared memory.
  if (lane == 0)
    reduce_smem[warp] = sum;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

// Block All Reduce Sum + float4
// grid(N/256), block(256/4)
// a: Nx1, y=sum(a)
template <const int NUM_THREADS = 256 / 4>
__global__ void block_all_reduce_sum_f32x4_f32_kernel(float *a, float *y,
                                                      int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 4;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];

  float4 reg_a = FLOAT4(a[idx]);
  // keep the data in register is enough for warp operaion.
  float sum = (idx < N) ? (reg_a.x + reg_a.y + reg_a.z + reg_a.w) : 0.0f;
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum = warp_reduce_sum_f32<WARP_SIZE>(sum);
  // warp leaders store the data to shared memory.
  if (lane == 0)
    reduce_smem[warp] = sum;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

//  FP16
//  Warp Reduce Sum: Half
template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ half warp_reduce_sum_f16_f16(half val) {
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val = __hadd(val, __shfl_xor_sync(0xffffffff, val, mask));
    // val += __shfl_xor_sync(0xffffffff, val, mask);
  }
  return val;
}

template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ float warp_reduce_sum_f16_f32(half val) {
  float val_f32 = __half2float(val);
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val_f32 += __shfl_xor_sync(0xffffffff, val_f32, mask);
  }
  return val_f32;
}

// Block All Reduce Sum: Half
// grid(N/256), block(256)
// a: Nx1, y=sum(a)
template <const int NUM_THREADS = 256>
__global__ void block_all_reduce_sum_f16_f16_kernel(half *a, float *y, int N) {
  int tid = threadIdx.x;
  int idx = blockIdx.x * NUM_THREADS + tid;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];
  // keep the data in register is enough for warp operaion.
  half sum_f16 = (idx < N) ? a[idx] : __float2half(0.0f);
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum_f16 = warp_reduce_sum_f16_f16<WARP_SIZE>(sum_f16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = __half2float(sum_f16);
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  float sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

template <const int NUM_THREADS = 256>
__global__ void block_all_reduce_sum_f16_f32_kernel(half *a, float *y, int N) {
  int tid = threadIdx.x;
  int idx = blockIdx.x * NUM_THREADS + tid;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];
  // keep the data in register is enough for warp operaion.
  half sum_f16 = (idx < N) ? a[idx] : __float2half(0.0f);
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  float sum_f32 = warp_reduce_sum_f16_f32<WARP_SIZE>(sum_f16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_f32;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  float sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

template <const int NUM_THREADS = 256 / 2>
__global__ void block_all_reduce_sum_f16x2_f32_kernel(half *a, float *y,
                                                      int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 2; // 2 half elements per thread
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];

  // keep the data in register is enough for warp operaion.
  half2 reg_a = HALF2(a[idx]);
  half sum_f16 = (idx < N) ? __hadd(reg_a.x, reg_a.y) : __float2half(0.0f);
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  float sum_f32 = warp_reduce_sum_f16_f32<WARP_SIZE>(sum_f16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_f32;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  float sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

template <const int NUM_THREADS = 256 / 2>
__global__ void block_all_reduce_sum_f16x2_f16_kernel(half *a, float *y,
                                                      int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 2; // 2 half elements per thread
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];

  // keep the data in register is enough for warp operaion.
  half2 reg_a = HALF2(a[idx]);
  half sum_f16 = (idx < N) ? __hadd(reg_a.x, reg_a.y) : __float2half(0.0f);
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum_f16 = warp_reduce_sum_f16_f16<WARP_SIZE>(sum_f16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = __half2float(sum_f16);
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  float sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

template <const int NUM_THREADS = 256 / 8>
__global__ void block_all_reduce_sum_f16x8_pack_f16_kernel(half *a, float *y,
                                                           int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 8; // 8 half elements per thread
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];

  const half z = __float2half(0.0f);
  half sum_f16 = z;

  // Safe element-wise loading instead of 128-bit access
#pragma unroll
  for (int i = 0; i < 8; ++i) {
    int global_idx = idx + i;
    if (global_idx < N) {
      sum_f16 += a[global_idx];
    }
  }

  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum_f16 = warp_reduce_sum_f16_f16<WARP_SIZE>(sum_f16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = __half2float(sum_f16);
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  float sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

template <const int NUM_THREADS = 256 / 8>
__global__ void block_all_reduce_sum_f16x8_pack_f32_kernel(half *a, float *y,
                                                           int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 8; // 8 half elements per thread
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];

  float sum_f32 = 0.0f;
  // Safe element-wise loading instead of 128-bit access
#pragma unroll
  for (int i = 0; i < 8; ++i) {
    int global_idx = idx + i;
    if (global_idx < N) {
      sum_f32 += __half2float(a[global_idx]);
    }
  }

  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum_f32 = warp_reduce_sum_f32<WARP_SIZE>(sum_f32);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_f32;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  float sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

//  BF16
//  Warp Reduce Sum: Half
template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ __nv_bfloat16
warp_reduce_sum_bf16_bf16(__nv_bfloat16 val) {
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val = __hadd(val, __shfl_xor_sync(0xffffffff, val, mask));
  }
  return val;
}

template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ float warp_reduce_sum_bf16_f32(__nv_bfloat16 val) {
  float val_f32 = __bfloat162float(val);
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val_f32 += __shfl_xor_sync(0xffffffff, val_f32, mask);
  }
  return val_f32;
}

// Block All Reduce Sum: BF16
// grid(N/256), block(256)
// a: Nx1, y=sum(a)
template <const int NUM_THREADS = 256>
__global__ void block_all_reduce_sum_bf16_bf16_kernel(__nv_bfloat16 *a,
                                                      float *y, int N) {
  int tid = threadIdx.x;
  int idx = blockIdx.x * NUM_THREADS + tid;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ __nv_bfloat16 reduce_smem[NUM_WARPS];

  // keep the data in register is enough for warp operaion.
  __nv_bfloat16 sum_bf16 = (idx < N) ? a[idx] : __float2bfloat16(0.0f);
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum_bf16 = warp_reduce_sum_bf16_bf16<WARP_SIZE>(sum_bf16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_bf16;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  __nv_bfloat16 sum =
      (lane < NUM_WARPS) ? reduce_smem[lane] : __float2bfloat16(0.0f);
  if (warp == 0)
    sum = warp_reduce_sum_bf16_bf16<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, __bfloat162float(sum));
}

template <const int NUM_THREADS = 256>
__global__ void block_all_reduce_sum_bf16_f32_kernel(__nv_bfloat16 *a, float *y,
                                                     int N) {
  int tid = threadIdx.x;
  int idx = blockIdx.x * NUM_THREADS + tid;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];

  // keep the data in register is enough for warp operaion.
  __nv_bfloat16 sum_bf16 = (idx < N) ? a[idx] : __float2bfloat16(0.0f);
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  float sum_f32 = warp_reduce_sum_bf16_f32<WARP_SIZE>(sum_bf16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_f32;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  float sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

template <const int NUM_THREADS = 256 / 2>
__global__ void block_all_reduce_sum_bf16x2_bf16_kernel(__nv_bfloat16 *a,
                                                        float *y, int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 2; // 2 bf16 elements per thread
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ __nv_bfloat16 reduce_smem[NUM_WARPS];

  // keep the data in register is enough for warp operaion.
  __nv_bfloat162 reg_a = BFLOAT2(a[idx]);
  __nv_bfloat16 sum_bf16 =
      (idx < N) ? __hadd(reg_a.x, reg_a.y) : __float2bfloat16(0.0f);
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum_bf16 = warp_reduce_sum_bf16_bf16<WARP_SIZE>(sum_bf16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_bf16;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  __nv_bfloat16 sum =
      (lane < NUM_WARPS) ? reduce_smem[lane] : __float2bfloat16(0.0f);
  if (warp == 0)
    sum = warp_reduce_sum_bf16_bf16<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, __bfloat162float(sum));
}

template <const int NUM_THREADS = 256 / 2>
__global__ void block_all_reduce_sum_bf16x2_f32_kernel(__nv_bfloat16 *a,
                                                       float *y, int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 2; // 2 bf16 elements per thread
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];

  // keep the data in register is enough for warp operaion.
  __nv_bfloat162 reg_a = BFLOAT2(a[idx]);
  __nv_bfloat16 sum_bf16 =
      (idx < N) ? __hadd(reg_a.x, reg_a.y) : __float2bfloat16(0.0f);
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  float sum_f32 = warp_reduce_sum_bf16_f32<WARP_SIZE>(sum_bf16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_f32;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  float sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

template <const int NUM_THREADS = 256 / 8>
__global__ void block_all_reduce_sum_bf16x8_pack_bf16_kernel(__nv_bfloat16 *a,
                                                             float *y, int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 8; // 8 bf16 elements per thread
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ __nv_bfloat16 reduce_smem[NUM_WARPS];

  const __nv_bfloat16 z = __float2bfloat16(0.0f);
  __nv_bfloat16 sum_bf16 = z;

  // Safe element-wise loading instead of 128-bit access
#pragma unroll
  for (int i = 0; i < 8; ++i) {
    int global_idx = idx + i;
    if (global_idx < N) {
      sum_bf16 += a[global_idx];
    }
  }

  // keep the data in register is enough for warp operaion.
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum_bf16 = warp_reduce_sum_bf16_bf16<WARP_SIZE>(sum_bf16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_bf16;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  __nv_bfloat16 sum = (lane < NUM_WARPS) ? reduce_smem[lane] : z;
  if (warp == 0)
    sum = warp_reduce_sum_bf16_bf16<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, __bfloat162float(sum));
}

template <const int NUM_THREADS = 256 / 8>
__global__ void block_all_reduce_sum_bf16x8_pack_f32_kernel(__nv_bfloat16 *a,
                                                            float *y, int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 8; // 8 bf16 elements per thread
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];

  const __nv_bfloat16 z = __float2bfloat16(0.0f);
  __nv_bfloat16 sum_bf16 = z;

  // Safe element-wise loading instead of 128-bit access
#pragma unroll
  for (int i = 0; i < 8; ++i) {
    int global_idx = idx + i;
    if (global_idx < N) {
      sum_bf16 += a[global_idx];
    }
  }

  // keep the data in register is enough for warp operaion.
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  float sum_f32 = warp_reduce_sum_bf16_f32<WARP_SIZE>(sum_bf16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp32 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_f32;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  float sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0)
    sum = warp_reduce_sum_f32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

//  FP8
//
template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ half
warp_reduce_sum_fp8_e4m3_f16(__nv_fp8_storage_t val) {
  // typedef unsigned char __nv_fp8_storage_t;
  // __half &operator=(const __half_raw &hr);
  half val_f16 = __nv_cvt_fp8_to_halfraw(val, __NV_E4M3);
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val_f16 = __hadd(val_f16, __shfl_xor_sync(0xffffffff, val_f16, mask));
  }
  return val_f16;
}

template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ half
warp_reduce_sum_fp8_e5m2_f16(__nv_fp8_storage_t val) {
  // typedef unsigned char __nv_fp8_storage_t;
  // __half &operator=(const __half_raw &hr);
  half val_f16 = __nv_cvt_fp8_to_halfraw(val, __NV_E5M2);
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val_f16 = __hadd(val_f16, __shfl_xor_sync(0xffffffff, val_f16, mask));
  }
  return val_f16;
}

template <const int NUM_THREADS = 256>
__global__ void block_all_reduce_sum_fp8_e4m3_f16_kernel(__nv_fp8_storage_t *a,
                                                         float *y, int N) {
  int tid = threadIdx.x;
  int idx = blockIdx.x * NUM_THREADS + tid;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ half reduce_smem[NUM_WARPS];

  // keep the data in register is enough for warp operaion.
  __nv_fp8_storage_t sum_f8 =
      (idx < N) ? a[idx]
                : __nv_cvt_float_to_fp8(0.0f, __NV_SATFINITE, __NV_E4M3);
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  half sum_f16 = warp_reduce_sum_fp8_e4m3_f16<WARP_SIZE>(sum_f8);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp16 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_f16;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  half sum = (lane < NUM_WARPS) ? reduce_smem[lane] : __float2half(0.0f);
  if (warp == 0)
    sum = warp_reduce_sum_f16_f16<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, __half2float(sum));
}

template <const int NUM_THREADS = 256>
__global__ void block_all_reduce_sum_fp8_e5m2_f16_kernel(__nv_fp8_storage_t *a,
                                                         float *y, int N) {
  int tid = threadIdx.x;
  int idx = blockIdx.x * NUM_THREADS + tid;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ half reduce_smem[NUM_WARPS];

  // keep the data in register is enough for warp operaion.
  __nv_fp8_storage_t sum_f8 =
      (idx < N) ? a[idx]
                : __nv_cvt_float_to_fp8(0.0f, __NV_SATFINITE, __NV_E5M2);
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  half sum_f16 = warp_reduce_sum_fp8_e5m2_f16<WARP_SIZE>(sum_f8);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp16 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_f16;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  half sum = (lane < NUM_WARPS) ? reduce_smem[lane] : __float2half(0.0f);
  if (warp == 0)
    sum = warp_reduce_sum_f16_f16<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, __half2float(sum));
}

template <const int NUM_THREADS = 256 / 16>
__global__ void
block_all_reduce_sum_fp8_e4m3x16_pack_f16_kernel(__nv_fp8_storage_t *a,
                                                 float *y, int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 16;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ half reduce_smem[NUM_WARPS];

  half sum_f16 = __float2half(0.0f);
  // Safe element-wise loading instead of 128-bit access
#pragma unroll
  for (int i = 0; i < 16; ++i) {
    int global_idx = idx + i;
    if (global_idx < N) {
      sum_f16 += __nv_cvt_fp8_to_halfraw(a[global_idx], __NV_E4M3);
    }
  }
  // keep the data in register is enough for warp operaion.
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum_f16 = warp_reduce_sum_f16_f16<WARP_SIZE>(sum_f16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp16 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_f16;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  half sum = (lane < NUM_WARPS) ? reduce_smem[lane] : __float2half(0.0f);
  if (warp == 0)
    sum = warp_reduce_sum_f16_f16<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, __half2float(sum));
}

template <const int NUM_THREADS = 256 / 16>
__global__ void
block_all_reduce_sum_fp8_e5m2x16_pack_f16_kernel(__nv_fp8_storage_t *a,
                                                 float *y, int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 16;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ half reduce_smem[NUM_WARPS];

  half sum_f16 = __float2half(0.0f);
  // Safe element-wise loading instead of 128-bit access
#pragma unroll
  for (int i = 0; i < 16; ++i) {
    int global_idx = idx + i;
    if (global_idx < N) {
      sum_f16 += __nv_cvt_fp8_to_halfraw(a[global_idx], __NV_E5M2);
    }
  }
  // keep the data in register is enough for warp operaion.
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum_f16 = warp_reduce_sum_f16_f16<WARP_SIZE>(sum_f16);
  // warp leaders store the data to shared memory.
  // use float to keep sum from each block and reduce
  // with fp16 inter warps.
  if (lane == 0)
    reduce_smem[warp] = sum_f16;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  half sum = (lane < NUM_WARPS) ? reduce_smem[lane] : __float2half(0.0f);
  if (warp == 0)
    sum = warp_reduce_sum_f16_f16<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, __half2float(sum));
}

//  INT8
//
template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ int32_t warp_reduce_sum_i8_i32(int8_t val) {
  int32_t val_i32 = static_cast<int32_t>(val);
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val_i32 += __shfl_xor_sync(0xffffffff, val_i32, mask);
  }
  return val_i32;
}

template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ int32_t warp_reduce_sum_i32_i32(int32_t val) {
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val += __shfl_xor_sync(0xffffffff, val, mask);
  }
  return val;
}

template <const int NUM_THREADS = 256>
__global__ void block_all_reduce_sum_i8_i32_kernel(int8_t *a, int32_t *y,
                                                   int N) {
  int tid = threadIdx.x;
  int idx = blockIdx.x * NUM_THREADS + tid;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ int32_t reduce_smem[NUM_WARPS];

  // keep the data in register is enough for warp operaion.
  int8_t sum_i8 = (idx < N) ? a[idx] : 0;
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  int32_t sum_i32 = warp_reduce_sum_i8_i32<WARP_SIZE>(sum_i8);
  if (lane == 0)
    reduce_smem[warp] = sum_i32;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  int32_t sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0;
  if (warp == 0)
    sum = warp_reduce_sum_i32_i32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

template <const int NUM_THREADS = 256 / 16>
__global__ void block_all_reduce_sum_i8x16_pack_i32_kernel(int8_t *a,
                                                           int32_t *y, int N) {
  int tid = threadIdx.x;
  int idx = (blockIdx.x * NUM_THREADS + tid) * 16;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ int32_t reduce_smem[NUM_WARPS];

  // keep the data in register is enough for warp operaion.
  int32_t sum_i32 = 0;
  // Safe element-wise loading instead of 128-bit access
#pragma unroll
  for (int i = 0; i < 16; ++i) {
    int global_idx = idx + i;
    if (global_idx < N) {
      sum_i32 += static_cast<int32_t>(a[global_idx]);
    }
  }

  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum_i32 = warp_reduce_sum_i32_i32<WARP_SIZE>(sum_i32);
  if (lane == 0)
    reduce_smem[warp] = sum_i32;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  int32_t sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0;
  if (warp == 0)
    sum = warp_reduce_sum_i32_i32<NUM_WARPS>(sum);
  if (tid == 0)
    atomicAdd(y, sum);
}

// Helper function to check CUDA errors
#define CHECK_CUDA(call) \
  do { \
    cudaError_t error = call; \
    if (error != cudaSuccess) { \
      printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(error)); \
      exit(1); \
    } \
  } while(0)

// CPU reference implementation for verification
template<typename T>
double cpu_reduce_sum(const std::vector<T>& input) {
  double sum = 0.0;
  for (const auto& val : input) {
    sum += static_cast<double>(val);
  }
  return sum;
}

// Test function for FP32 kernels
void test_f32_kernels(int N) {
  printf("Testing FP32 reduce kernels with N=%d\n", N);

  // Host memory allocation
  std::vector<float> h_input(N);

  // Initialize input data
  for (int i = 0; i < N; i++) {
    h_input[i] = static_cast<float>(i % 100) / 10.0f + 1.0f;
  }

  // Compute reference result on CPU
  double cpu_sum = cpu_reduce_sum(h_input);
  printf("CPU reference sum: %.6f\n", cpu_sum);

  // Device memory allocation
  float *d_input, *d_output;
  CHECK_CUDA(cudaMalloc(&d_input, N * sizeof(float)));
  CHECK_CUDA(cudaMalloc(&d_output, sizeof(float)));

  // Copy input data to device
  CHECK_CUDA(cudaMemcpy(d_input, h_input.data(), N * sizeof(float), cudaMemcpyHostToDevice));

  cudaEvent_t start, stop;
  CHECK_CUDA(cudaEventCreate(&start));
  CHECK_CUDA(cudaEventCreate(&stop));

  // Test basic FP32 kernel
  constexpr int NUM_THREADS = 256;
  dim3 block(NUM_THREADS);
  dim3 grid((N + NUM_THREADS - 1) / NUM_THREADS);

  // Initialize output to zero
  CHECK_CUDA(cudaMemset(d_output, 0, sizeof(float)));

  CHECK_CUDA(cudaEventRecord(start));
  block_all_reduce_sum_f32_f32_kernel<NUM_THREADS><<<grid, block>>>(d_input, d_output, N);
  CHECK_CUDA(cudaEventRecord(stop));
  CHECK_CUDA(cudaGetLastError());
  CHECK_CUDA(cudaDeviceSynchronize());

  float milliseconds;
  CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));

  // Copy result back and verify
  float gpu_sum;
  CHECK_CUDA(cudaMemcpy(&gpu_sum, d_output, sizeof(float), cudaMemcpyDeviceToHost));

  double error = fabs(gpu_sum - cpu_sum);
  bool correct = error < 1e-3; // Relaxed tolerance for large sums
  printf("FP32 basic kernel: %s (%.3f ms, error=%.2e)\n",
         correct ? "PASSED" : "FAILED", milliseconds, error);
  printf("  GPU result: %.6f, CPU result: %.6f\n", gpu_sum, cpu_sum);

  // Test FP32x4 vectorized kernel
  if (N % 4 == 0) {
    constexpr int NUM_THREADS_VEC = NUM_THREADS / 4;
    dim3 block_vec(NUM_THREADS_VEC);
    dim3 grid_vec((N/4 + NUM_THREADS_VEC - 1) / NUM_THREADS_VEC);

    CHECK_CUDA(cudaMemset(d_output, 0, sizeof(float)));
    CHECK_CUDA(cudaEventRecord(start));
    block_all_reduce_sum_f32x4_f32_kernel<NUM_THREADS_VEC><<<grid_vec, block_vec>>>(d_input, d_output, N);
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    CHECK_CUDA(cudaMemcpy(&gpu_sum, d_output, sizeof(float), cudaMemcpyDeviceToHost));

    error = fabs(gpu_sum - cpu_sum);
    correct = error < 1e-3;
    printf("FP32x4 vectorized kernel: %s (%.3f ms, error=%.2e)\n",
           correct ? "PASSED" : "FAILED", milliseconds, error);
    printf("  GPU result: %.6f, CPU result: %.6f\n", gpu_sum, cpu_sum);
  }

  // Cleanup
  CHECK_CUDA(cudaFree(d_input));
  CHECK_CUDA(cudaFree(d_output));
  CHECK_CUDA(cudaEventDestroy(start));
  CHECK_CUDA(cudaEventDestroy(stop));
}

// Test function for FP16 kernels
void test_f16_kernels(int N) {
  printf("Testing FP16 reduce kernels with N=%d\n", N);

  // Host memory allocation
  std::vector<half> h_input(N);
  std::vector<float> h_input_float(N);

  // Initialize input data
  for (int i = 0; i < N; i++) {
    float val = static_cast<float>(i % 100) / 10.0f + 1.0f;
    h_input_float[i] = val;
    h_input[i] = __float2half(val);
  }

  // Compute reference result on CPU
  double cpu_sum = cpu_reduce_sum(h_input_float);
  printf("CPU reference sum: %.6f\n", cpu_sum);

  // Device memory allocation
  half *d_input;
  float *d_output;
  CHECK_CUDA(cudaMalloc(&d_input, N * sizeof(half)));
  CHECK_CUDA(cudaMalloc(&d_output, sizeof(float)));

  // Copy input data to device
  CHECK_CUDA(cudaMemcpy(d_input, h_input.data(), N * sizeof(half), cudaMemcpyHostToDevice));

  cudaEvent_t start, stop;
  CHECK_CUDA(cudaEventCreate(&start));
  CHECK_CUDA(cudaEventCreate(&stop));

  constexpr int NUM_THREADS = 256;
  dim3 block(NUM_THREADS);
  dim3 grid((N + NUM_THREADS - 1) / NUM_THREADS);

  // Test FP16->FP16 kernel
  CHECK_CUDA(cudaMemset(d_output, 0, sizeof(float)));

  float milliseconds;
  CHECK_CUDA(cudaEventRecord(start));
  block_all_reduce_sum_f16_f16_kernel<NUM_THREADS><<<grid, block>>>(d_input, d_output, N);
  CHECK_CUDA(cudaEventRecord(stop));
  CHECK_CUDA(cudaGetLastError());
  CHECK_CUDA(cudaDeviceSynchronize());

  CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));

  float gpu_sum;
  CHECK_CUDA(cudaMemcpy(&gpu_sum, d_output, sizeof(float), cudaMemcpyDeviceToHost));

  double error = fabs(gpu_sum - cpu_sum);
  bool correct = error < 1e-2; // Looser tolerance for FP16
  printf("FP16->FP16 kernel: %s (%.3f ms, error=%.2e)\n",
         correct ? "PASSED" : "FAILED", milliseconds, error);
  printf("  GPU result: %.6f, CPU result: %.6f\n", gpu_sum, cpu_sum);

  // Test FP16->FP32 kernel
  CHECK_CUDA(cudaMemset(d_output, 0, sizeof(float)));
  CHECK_CUDA(cudaEventRecord(start));
  block_all_reduce_sum_f16_f32_kernel<NUM_THREADS><<<grid, block>>>(d_input, d_output, N);
  CHECK_CUDA(cudaEventRecord(stop));
  CHECK_CUDA(cudaGetLastError());
  CHECK_CUDA(cudaDeviceSynchronize());

  CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
  CHECK_CUDA(cudaMemcpy(&gpu_sum, d_output, sizeof(float), cudaMemcpyDeviceToHost));

  error = fabs(gpu_sum - cpu_sum);
  correct = error < 1e-2;
  printf("FP16->FP32 kernel: %s (%.3f ms, error=%.2e)\n",
         correct ? "PASSED" : "FAILED", milliseconds, error);
  printf("  GPU result: %.6f, CPU result: %.6f\n", gpu_sum, cpu_sum);

  // Test FP16x2 kernels
  if (N % 2 == 0) {
    constexpr int NUM_THREADS_VEC = NUM_THREADS / 2;
    dim3 block_vec(NUM_THREADS_VEC);
    dim3 grid_vec((N/2 + NUM_THREADS_VEC - 1) / NUM_THREADS_VEC);

    // FP16x2->FP32 kernel
    CHECK_CUDA(cudaMemset(d_output, 0, sizeof(float)));
    CHECK_CUDA(cudaEventRecord(start));
    block_all_reduce_sum_f16x2_f32_kernel<NUM_THREADS_VEC><<<grid_vec, block_vec>>>(d_input, d_output, N);
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    CHECK_CUDA(cudaMemcpy(&gpu_sum, d_output, sizeof(float), cudaMemcpyDeviceToHost));

    error = fabs(gpu_sum - cpu_sum);
    correct = error < 1e-2;
    printf("FP16x2->FP32 kernel: %s (%.3f ms, error=%.2e)\n",
           correct ? "PASSED" : "FAILED", milliseconds, error);
    printf("  GPU result: %.6f, CPU result: %.6f\n", gpu_sum, cpu_sum);

    // FP16x2->FP16 kernel
    CHECK_CUDA(cudaMemset(d_output, 0, sizeof(float)));
    CHECK_CUDA(cudaEventRecord(start));
    block_all_reduce_sum_f16x2_f16_kernel<NUM_THREADS_VEC><<<grid_vec, block_vec>>>(d_input, d_output, N);
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    CHECK_CUDA(cudaMemcpy(&gpu_sum, d_output, sizeof(float), cudaMemcpyDeviceToHost));

    error = fabs(gpu_sum - cpu_sum);
    correct = error < 1e-2;
    printf("FP16x2->FP16 kernel: %s (%.3f ms, error=%.2e)\n",
           correct ? "PASSED" : "FAILED", milliseconds, error);
    printf("  GPU result: %.6f, CPU result: %.6f\n", gpu_sum, cpu_sum);
  }

  // Test FP16x8 pack kernels
  if (N % 8 == 0) {
    constexpr int NUM_THREADS_PACK = NUM_THREADS / 8;
    dim3 block_pack(NUM_THREADS_PACK);
    dim3 grid_pack((N/8 + NUM_THREADS_PACK - 1) / NUM_THREADS_PACK);

    // FP16x8 pack->FP16 kernel
    CHECK_CUDA(cudaMemset(d_output, 0, sizeof(float)));
    CHECK_CUDA(cudaEventRecord(start));
    block_all_reduce_sum_f16x8_pack_f16_kernel<NUM_THREADS_PACK><<<grid_pack, block_pack>>>(d_input, d_output, N);
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    CHECK_CUDA(cudaMemcpy(&gpu_sum, d_output, sizeof(float), cudaMemcpyDeviceToHost));

    error = fabs(gpu_sum - cpu_sum);
    correct = error < 1e-2;
    printf("FP16x8 pack->FP16 kernel: %s (%.3f ms, error=%.2e)\n",
           correct ? "PASSED" : "FAILED", milliseconds, error);
    printf("  GPU result: %.6f, CPU result: %.6f\n", gpu_sum, cpu_sum);

    // FP16x8 pack->FP32 kernel
    CHECK_CUDA(cudaMemset(d_output, 0, sizeof(float)));
    CHECK_CUDA(cudaEventRecord(start));
    block_all_reduce_sum_f16x8_pack_f32_kernel<NUM_THREADS_PACK><<<grid_pack, block_pack>>>(d_input, d_output, N);
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    CHECK_CUDA(cudaMemcpy(&gpu_sum, d_output, sizeof(float), cudaMemcpyDeviceToHost));

    error = fabs(gpu_sum - cpu_sum);
    correct = error < 1e-2;
    printf("FP16x8 pack->FP32 kernel: %s (%.3f ms, error=%.2e)\n",
           correct ? "PASSED" : "FAILED", milliseconds, error);
    printf("  GPU result: %.6f, CPU result: %.6f\n", gpu_sum, cpu_sum);
  }

  // Cleanup
  CHECK_CUDA(cudaFree(d_input));
  CHECK_CUDA(cudaFree(d_output));
  CHECK_CUDA(cudaEventDestroy(start));
  CHECK_CUDA(cudaEventDestroy(stop));
}

// Test function for INT8 kernels
void test_i8_kernels(int N) {
  printf("Testing INT8 reduce kernels with N=%d\n", N);

  // Host memory allocation
  std::vector<int8_t> h_input(N);

  // Initialize input data
  for (int i = 0; i < N; i++) {
    h_input[i] = static_cast<int8_t>((i % 20) - 10); // Range [-10, 9]
  }

  // Compute reference result on CPU
  int64_t cpu_sum = 0;
  for (const auto& val : h_input) {
    cpu_sum += static_cast<int64_t>(val);
  }
  printf("CPU reference sum: %ld\n", cpu_sum);

  // Device memory allocation
  int8_t *d_input;
  int32_t *d_output;
  CHECK_CUDA(cudaMalloc(&d_input, N * sizeof(int8_t)));
  CHECK_CUDA(cudaMalloc(&d_output, sizeof(int32_t)));

  // Copy input data to device
  CHECK_CUDA(cudaMemcpy(d_input, h_input.data(), N * sizeof(int8_t), cudaMemcpyHostToDevice));

  cudaEvent_t start, stop;
  CHECK_CUDA(cudaEventCreate(&start));
  CHECK_CUDA(cudaEventCreate(&stop));

  constexpr int NUM_THREADS = 256;
  dim3 block(NUM_THREADS);
  dim3 grid((N + NUM_THREADS - 1) / NUM_THREADS);

  // Test basic INT8 kernel
  CHECK_CUDA(cudaMemset(d_output, 0, sizeof(int32_t)));

  float milliseconds;
  CHECK_CUDA(cudaEventRecord(start));
  block_all_reduce_sum_i8_i32_kernel<NUM_THREADS><<<grid, block>>>(d_input, d_output, N);
  CHECK_CUDA(cudaEventRecord(stop));
  CHECK_CUDA(cudaGetLastError());
  CHECK_CUDA(cudaDeviceSynchronize());

  CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));

  int32_t gpu_sum;
  CHECK_CUDA(cudaMemcpy(&gpu_sum, d_output, sizeof(int32_t), cudaMemcpyDeviceToHost));

  int64_t error = abs(static_cast<int64_t>(gpu_sum) - cpu_sum);
  bool correct = error == 0;
  printf("INT8 basic kernel: %s (%.3f ms, error=%ld)\n",
         correct ? "PASSED" : "FAILED", milliseconds, error);
  printf("  GPU result: %d, CPU result: %ld\n", gpu_sum, cpu_sum);

  // Test INT8x16 pack kernel
  if (N % 16 == 0) {
    constexpr int NUM_THREADS_PACK = NUM_THREADS / 16;
    dim3 block_pack(NUM_THREADS_PACK);
    dim3 grid_pack((N/16 + NUM_THREADS_PACK - 1) / NUM_THREADS_PACK);

    CHECK_CUDA(cudaMemset(d_output, 0, sizeof(int32_t)));
    CHECK_CUDA(cudaEventRecord(start));
    block_all_reduce_sum_i8x16_pack_i32_kernel<NUM_THREADS_PACK><<<grid_pack, block_pack>>>(d_input, d_output, N);
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    CHECK_CUDA(cudaMemcpy(&gpu_sum, d_output, sizeof(int32_t), cudaMemcpyDeviceToHost));

    error = abs(static_cast<int64_t>(gpu_sum) - cpu_sum);
    correct = error == 0;
    printf("INT8x16 pack kernel: %s (%.3f ms, error=%ld)\n",
           correct ? "PASSED" : "FAILED", milliseconds, error);
    printf("  GPU result: %d, CPU result: %ld\n", gpu_sum, cpu_sum);
  }

  // Cleanup
  CHECK_CUDA(cudaFree(d_input));
  CHECK_CUDA(cudaFree(d_output));
  CHECK_CUDA(cudaEventDestroy(start));
  CHECK_CUDA(cudaEventDestroy(stop));
}

int main(int argc, char* argv[]) {
  printf("CUDA Block All Reduce Kernels Test\n");
  printf("==================================\n");

  // Check CUDA device
  int deviceCount;
  CHECK_CUDA(cudaGetDeviceCount(&deviceCount));
  if (deviceCount == 0) {
    printf("No CUDA devices found!\n");
    return 1;
  }

  cudaDeviceProp prop;
  CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
  printf("Using device: %s\n", prop.name);
  printf("Compute capability: %d.%d\n", prop.major, prop.minor);
  printf("Warp size: %d\n", prop.warpSize);
  printf("Max threads per block: %d\n", prop.maxThreadsPerBlock);
  printf("\n");

  // Parse command line arguments
  std::vector<int> test_sizes;

  if (argc > 1) {
    // Use command line arguments as test sizes
    printf("Using custom test sizes from command line:\n");
    for (int i = 1; i < argc; i++) {
      int size = atoi(argv[i]);
      if (size > 0) {
        test_sizes.push_back(size);
        printf("  - %d\n", size);
      } else {
        printf("Warning: Invalid size '%s' ignored\n", argv[i]);
      }
    }
    printf("\n");
  } else {
    // Use default test sizes
    test_sizes = {1024, 4096, 16384, 65536, 262144};
    printf("Using default test sizes: 1024, 4096, 16384, 65536, 262144\n");
    printf("Usage: %s [size1] [size2] ... to specify custom sizes\n\n", argv[0]);
  }

  if (test_sizes.empty()) {
    printf("No valid test sizes provided!\n");
    return 1;
  }

  // Test different data types and sizes
  for (int N : test_sizes) {
    printf("========================================\n");
    printf("Testing with N = %d elements\n", N);
    printf("========================================\n");

    test_f32_kernels(N);
    printf("\n");

    test_f16_kernels(N);
    printf("\n");

    test_i8_kernels(N);
    printf("\n");
  }

  // Performance comparison for the largest test case
  if (!test_sizes.empty()) {
    int N = test_sizes.back();
    printf("Performance Comparison (N=%d):\n", N);
    printf("=============================\n");

    const int num_runs = 100;

    // Prepare test data
    std::vector<float> h_input_f32(N);
    std::vector<half> h_input_f16(N);
    std::vector<int8_t> h_input_i8(N);

    for (int i = 0; i < N; i++) {
      float val = static_cast<float>(i % 100) / 10.0f + 1.0f;
      h_input_f32[i] = val;
      h_input_f16[i] = __float2half(val);
      h_input_i8[i] = static_cast<int8_t>((i % 20) - 10);
    }

    // FP32 performance test
    {
      float *d_input, *d_output;
      CHECK_CUDA(cudaMalloc(&d_input, N * sizeof(float)));
      CHECK_CUDA(cudaMalloc(&d_output, sizeof(float)));
      CHECK_CUDA(cudaMemcpy(d_input, h_input_f32.data(), N * sizeof(float), cudaMemcpyHostToDevice));

      cudaEvent_t start, stop;
      CHECK_CUDA(cudaEventCreate(&start));
      CHECK_CUDA(cudaEventCreate(&stop));

      constexpr int NUM_THREADS = 256;
      dim3 block(NUM_THREADS);
      dim3 grid((N + NUM_THREADS - 1) / NUM_THREADS);

      float total_time = 0.0f;
      for (int run = 0; run < num_runs; run++) {
        CHECK_CUDA(cudaMemset(d_output, 0, sizeof(float)));
        CHECK_CUDA(cudaEventRecord(start));
        block_all_reduce_sum_f32_f32_kernel<NUM_THREADS><<<grid, block>>>(d_input, d_output, N);
        CHECK_CUDA(cudaEventRecord(stop));
        CHECK_CUDA(cudaDeviceSynchronize());

        float milliseconds;
        CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
        total_time += milliseconds;
      }

      float avg_time = total_time / num_runs;
      float bandwidth = (N * sizeof(float)) / (avg_time * 1e-3) / (1024.0f * 1024.0f * 1024.0f);
      printf("FP32 basic kernel avg time: %.3f ms (%.2f GB/s)\n", avg_time, bandwidth);

      CHECK_CUDA(cudaFree(d_input));
      CHECK_CUDA(cudaFree(d_output));
      CHECK_CUDA(cudaEventDestroy(start));
      CHECK_CUDA(cudaEventDestroy(stop));
    }

    printf("\nNote: This implementation demonstrates various reduce patterns:\n");
    printf("- Warp-level shuffle reductions\n");
    printf("- Block-level shared memory reductions\n");
    printf("- Vectorized memory access patterns\n");
    printf("- Mixed precision arithmetic\n");
    printf("- Atomic operations for final accumulation\n");
  }

  printf("\nAll tests completed!\n");
  return 0;
}