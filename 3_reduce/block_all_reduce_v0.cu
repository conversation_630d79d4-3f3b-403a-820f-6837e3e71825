
#include <algorithm>
#include <cuda_runtime.h>
#include <stdio.h>
#include <stdlib.h>
#include <vector>
#include <chrono>
#include <cmath>
#include <iostream>

#define WARP_SIZE 32

#define checkCudaErrors(func)                                                  \
  {                                                                            \
    cudaError_t e = (func);                                                    \
    if (e != cudaSuccess)                                                      \
      printf("%s %d CUDA: %s\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
  }

// Warp reduce sum function
template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ float warp_reduce_sum(float val) {
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val += __shfl_xor_sync(0xffffffff, val, mask);
  }
  return val;
}

// Block All Reduce Sum
// grid(N/128), block(128)
// a: Nx1, y=sum(a)
template<const int NUM_THREADS = 128>
__global__ void block_all_reduce_sum(float* a, float* y, int N) {
  int tid = threadIdx.x;
  int idx = blockIdx.x * NUM_THREADS + tid;
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];
  // keep the data in register is enougth for warp operaion.
  float sum = (idx < N) ? a[idx] : 0.0f;
  int warp = tid / WARP_SIZE;
  int lane = tid % WARP_SIZE;
  // perform warp sync reduce.
  sum = warp_reduce_sum<WARP_SIZE>(sum);
  // warp leaders store the data to shared memory.
  if (lane == 0) reduce_smem[warp] = sum;
  __syncthreads(); // make sure the data is in shared memory.
  // the first warp compute the final sum.
  sum = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  if (warp == 0) sum = warp_reduce_sum<NUM_WARPS>(sum);
  if (tid == 0) atomicAdd(y, sum);
}

// CPU reference implementation
float reduce_sum_cpu(const float* a, int N) {
  float sum = 0.0f;
  for (int i = 0; i < N; i++) {
    sum += a[i];
  }
  return sum;
}

// Initialize vector with test data
void init_vector(float* a, int N) {
  for (int i = 0; i < N; i++) {
    a[i] = static_cast<float>(i % 100) / 100.0f; // Range [0, 0.99]
  }
}

// Performance testing function
template<typename KernelFunc>
double benchmark_kernel(KernelFunc kernel_func, float* d_a, float* d_y,
                       int N, int num_iterations = 1000) {
  // Warm up
  for (int i = 0; i < 10; i++) {
    checkCudaErrors(cudaMemset(d_y, 0, sizeof(float)));
    kernel_func(d_a, d_y, N);
  }
  checkCudaErrors(cudaDeviceSynchronize());

  // Timing
  auto start = std::chrono::high_resolution_clock::now();
  for (int i = 0; i < num_iterations; i++) {
    checkCudaErrors(cudaMemset(d_y, 0, sizeof(float)));
    kernel_func(d_a, d_y, N);
  }
  checkCudaErrors(cudaDeviceSynchronize());
  auto end = std::chrono::high_resolution_clock::now();

  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
  return duration.count() / 1000.0 / num_iterations; // ms per iteration
}

int main(int argc, char **argv) {
  // Parse command line arguments
  int N = 1024 * 1024;
  if (argc >= 2) {
    N = atoi(argv[1]);
  }

  printf("Block All Reduce v0 Performance Test\n");
  printf("Vector size: %d\n", N);
  printf("=================================\n");

  // Allocate host memory
  size_t size = N * sizeof(float);
  float *h_a = (float*)malloc(size);
  float h_result_gpu = 0.0f;

  // Initialize input vector
  init_vector(h_a, N);

  // Allocate device memory
  float *d_a, *d_y;
  checkCudaErrors(cudaMalloc(&d_a, size));
  checkCudaErrors(cudaMalloc(&d_y, sizeof(float)));

  // Copy input data to device
  checkCudaErrors(cudaMemcpy(d_a, h_a, size, cudaMemcpyHostToDevice));

  // Compute CPU reference
  printf("Computing CPU reference...\n");
  auto cpu_start = std::chrono::high_resolution_clock::now();
  float h_result_cpu = reduce_sum_cpu(h_a, N);
  auto cpu_end = std::chrono::high_resolution_clock::now();
  auto cpu_time = std::chrono::duration_cast<std::chrono::microseconds>(cpu_end - cpu_start);
  printf("CPU result: %.6f, time: %.3f ms\n\n", h_result_cpu, cpu_time.count() / 1000.0);

  // Test GPU kernel
  printf("Testing GPU block all reduce kernel...\n");
  constexpr int NUM_THREADS = 128;
  dim3 block(NUM_THREADS);
  dim3 grid((N + NUM_THREADS - 1) / NUM_THREADS);

  checkCudaErrors(cudaMemset(d_y, 0, sizeof(float)));
  block_all_reduce_sum<NUM_THREADS><<<grid, block>>>(d_a, d_y, N);
  checkCudaErrors(cudaGetLastError());
  checkCudaErrors(cudaDeviceSynchronize());

  checkCudaErrors(cudaMemcpy(&h_result_gpu, d_y, sizeof(float), cudaMemcpyDeviceToHost));

  // Verify result
  float diff = std::abs(h_result_gpu - h_result_cpu);
  float rel_err = diff / std::abs(h_result_cpu);
  bool passed = rel_err < 1e-3f; // More lenient tolerance for large reductions

  printf("GPU result: %.6f\n", h_result_gpu);
  printf("Absolute error: %.6e\n", diff);
  printf("Relative error: %.6e\n", rel_err);
  printf("Verification: %s\n", passed ? "PASSED" : "FAILED");

  if (passed) {
    // Benchmark performance
    double time_ms = benchmark_kernel(
        [&](float *a, float *y, int n) {
          block_all_reduce_sum<NUM_THREADS><<<grid, block>>>(a, y, n);
        },
        d_a, d_y, N);

    double bandwidth = size / (time_ms * 1e-3) / 1e9; // GB/s (read only)
    printf("Performance: %.3f ms, %.2f GB/s\n", time_ms, bandwidth);
  }

  printf("\n=================================\n");
  printf("Overall result: %s\n", passed ? "PASSED" : "FAILED");

  // Cleanup
  free(h_a);
  checkCudaErrors(cudaFree(d_a));
  checkCudaErrors(cudaFree(d_y));

  return passed ? 0 : 1;
}