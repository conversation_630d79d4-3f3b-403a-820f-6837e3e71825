add_executable(reduce_v0_baseline reduce_v0_baseline.cu)
target_link_libraries(reduce_v0_baseline PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(reduce_v0_baseline PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(reduce_v0_baseline PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(reduce_v1_no_divergence_branch reduce_v1_no_divergence_branch.cu)
target_link_libraries(reduce_v1_no_divergence_branch PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(reduce_v1_no_divergence_branch PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(reduce_v1_no_divergence_branch PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(reduce_v2_no_bank_conflict reduce_v2_no_bank_conflict.cu)
target_link_libraries(reduce_v2_no_bank_conflict PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(reduce_v2_no_bank_conflict PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(reduce_v2_no_bank_conflict PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(reduce_v3_add_during_load reduce_v3_add_during_load.cu)
target_link_libraries(reduce_v3_add_during_load PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(reduce_v3_add_during_load PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(reduce_v3_add_during_load PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(reduce_v4_unroll_last_warp reduce_v4_unroll_last_warp.cu)
target_link_libraries(reduce_v4_unroll_last_warp PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(reduce_v4_unroll_last_warp PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(reduce_v4_unroll_last_warp PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(reduce_v5_completely_unroll reduce_v5_completely_unroll.cu)
target_link_libraries(reduce_v5_completely_unroll PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(reduce_v5_completely_unroll PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(reduce_v5_completely_unroll PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(reduce_v6_multi_add reduce_v6_multi_add.cu)
target_link_libraries(reduce_v6_multi_add PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(reduce_v6_multi_add PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(reduce_v6_multi_add PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(reduce_v7_shuffle reduce_v7_shuffle.cu)
target_link_libraries(reduce_v7_shuffle PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(reduce_v7_shuffle PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(reduce_v7_shuffle PROPERTIES CUDA_SEPARABLE_COMPILATION ON)
target_compile_options(reduce_v7_shuffle PRIVATE -lineinfo)

add_executable(block_all_reduce block_all_reduce.cu)
target_link_libraries(block_all_reduce PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(block_all_reduce PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(block_all_reduce PROPERTIES CUDA_SEPARABLE_COMPILATION ON)
target_compile_options(block_all_reduce PRIVATE -lineinfo)

add_executable(block_all_reduce_v0 block_all_reduce_v0.cu)
target_link_libraries(block_all_reduce_v0 PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(block_all_reduce_v0 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(block_all_reduce_v0 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(block_all_reduce_v1 block_all_reduce_v1.cu)
target_link_libraries(block_all_reduce_v1 PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(block_all_reduce_v1 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(block_all_reduce_v1 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)