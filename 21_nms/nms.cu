
#include <algorithm>
#include <cuda_runtime.h>
#include <stdio.h>
#include <stdlib.h>
#include <vector>
#include <chrono>
#include <cmath>
#include <iostream>
#include <random>
#include <cassert>

#define checkCudaErrors(func)                                                  \
  {                                                                            \
    cudaError_t e = (func);                                                    \
    if (e != cudaSuccess)                                                      \
      printf("%s %d CUDA: %s\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
  }

struct Box {
  float x1, y1, x2, y2, score;

  __host__ __device__ float area() const {
    return (std::abs(x2 - x1 + 1)) * std::abs(y2 - y1 + 1);
  }

  __host__ __device__ float iou_of(const Box& other) const {
    float inner_x1 = x1 > other.x1 ? x1 : other.x1;
    float inner_y1 = y1 > other.y1 ? y1 : other.y1;
    float inner_x2 = x2 < other.x2 ? x2 : other.x2;
    float inner_y2 = y2 < other.y2 ? y2 : other.y2;
    float inner_h = inner_y2 - inner_y1 + 1.0f;
    float inner_w = inner_x2 - inner_x1 + 1.0f;

    if (inner_h <= 0 || inner_w <= 0) return 0.0f;

    float inner_area = inner_h * inner_w;
    return (inner_area / (area() + other.area() - inner_area));
  }
};

// CPU implementation of Hard NMS
void hard_nms_cpu(std::vector<Box> &input, std::vector<Box> &output, float iou_threshold) {
  if (input.empty()) return;

  // Sort boxes by score in descending order
  std::sort(input.begin(), input.end(), [](const Box& a, const Box& b) {
    return a.score > b.score;
  });

  int box_num = input.size();
  std::vector<int> merged(box_num, 0);

  for (int i = 0; i < box_num; ++i) {
    if (merged[i]) continue;
    merged[i] = 1;

    for (int j = i + 1; j < box_num; ++j) {
      if (merged[j]) continue;
      float iou = input[i].iou_of(input[j]);
      if (iou > iou_threshold) {
        merged[j] = 1;
      }
    }
    output.push_back(input[i]);
  }
}

// GPU kernel for computing IoU matrix
__global__ void compute_iou_matrix(Box* boxes, float* iou_matrix, int num_boxes) {
  int i = blockIdx.x * blockDim.x + threadIdx.x;
  int j = blockIdx.y * blockDim.y + threadIdx.y;

  if (i < num_boxes && j < num_boxes && i != j) {
    float iou = boxes[i].iou_of(boxes[j]);
    iou_matrix[i * num_boxes + j] = iou;
  }
}

// GPU implementation of Hard NMS
void hard_nms_gpu(std::vector<Box> &input, std::vector<Box> &output, float iou_threshold) {
  if (input.empty()) return;

  // Sort boxes by score in descending order
  std::sort(input.begin(), input.end(), [](const Box& a, const Box& b) {
    return a.score > b.score;
  });

  int num_boxes = input.size();

  // Allocate device memory
  Box* d_boxes;
  float* d_iou_matrix;
  checkCudaErrors(cudaMalloc(&d_boxes, num_boxes * sizeof(Box)));
  checkCudaErrors(cudaMalloc(&d_iou_matrix, num_boxes * num_boxes * sizeof(float)));

  // Copy boxes to device
  checkCudaErrors(cudaMemcpy(d_boxes, input.data(), num_boxes * sizeof(Box), cudaMemcpyHostToDevice));

  // Compute IoU matrix on GPU
  dim3 block(16, 16);
  dim3 grid((num_boxes + block.x - 1) / block.x, (num_boxes + block.y - 1) / block.y);
  compute_iou_matrix<<<grid, block>>>(d_boxes, d_iou_matrix, num_boxes);
  checkCudaErrors(cudaGetLastError());
  checkCudaErrors(cudaDeviceSynchronize());

  // Copy IoU matrix back to host
  std::vector<float> iou_matrix(num_boxes * num_boxes);
  checkCudaErrors(cudaMemcpy(iou_matrix.data(), d_iou_matrix,
                            num_boxes * num_boxes * sizeof(float), cudaMemcpyDeviceToHost));

  // Perform NMS on CPU using precomputed IoU matrix
  std::vector<int> merged(num_boxes, 0);
  for (int i = 0; i < num_boxes; ++i) {
    if (merged[i]) continue;
    merged[i] = 1;

    for (int j = i + 1; j < num_boxes; ++j) {
      if (merged[j]) continue;
      float iou = iou_matrix[i * num_boxes + j];
      if (iou > iou_threshold) {
        merged[j] = 1;
      }
    }
    output.push_back(input[i]);
  }

  // Cleanup
  checkCudaErrors(cudaFree(d_boxes));
  checkCudaErrors(cudaFree(d_iou_matrix));
}

// Generate random test boxes
std::vector<Box> generate_test_boxes(int num_boxes, float max_coord = 1000.0f) {
  std::vector<Box> boxes;
  std::random_device rd;
  std::mt19937 gen(42); // Fixed seed for reproducibility
  std::uniform_real_distribution<float> coord_dist(0.0f, max_coord);
  std::uniform_real_distribution<float> score_dist(0.0f, 1.0f);

  for (int i = 0; i < num_boxes; ++i) {
    Box box;
    box.x1 = coord_dist(gen);
    box.y1 = coord_dist(gen);
    box.x2 = box.x1 + coord_dist(gen) * 0.1f; // Ensure x2 > x1
    box.y2 = box.y1 + coord_dist(gen) * 0.1f; // Ensure y2 > y1
    box.score = score_dist(gen);
    boxes.push_back(box);
  }

  return boxes;
}

// Verify results between CPU and GPU implementations
bool verify_results(const std::vector<Box>& cpu_result, const std::vector<Box>& gpu_result, float tolerance = 1e-5f) {
  if (cpu_result.size() != gpu_result.size()) {
    printf("Size mismatch: CPU=%zu, GPU=%zu\n", cpu_result.size(), gpu_result.size());
    return false;
  }

  for (size_t i = 0; i < cpu_result.size(); ++i) {
    const Box& cpu_box = cpu_result[i];
    const Box& gpu_box = gpu_result[i];

    if (std::abs(cpu_box.x1 - gpu_box.x1) > tolerance ||
        std::abs(cpu_box.y1 - gpu_box.y1) > tolerance ||
        std::abs(cpu_box.x2 - gpu_box.x2) > tolerance ||
        std::abs(cpu_box.y2 - gpu_box.y2) > tolerance ||
        std::abs(cpu_box.score - gpu_box.score) > tolerance) {
      printf("Box %zu mismatch:\n", i);
      printf("  CPU: (%.3f,%.3f,%.3f,%.3f) score=%.3f\n",
             cpu_box.x1, cpu_box.y1, cpu_box.x2, cpu_box.y2, cpu_box.score);
      printf("  GPU: (%.3f,%.3f,%.3f,%.3f) score=%.3f\n",
             gpu_box.x1, gpu_box.y1, gpu_box.x2, gpu_box.y2, gpu_box.score);
      return false;
    }
  }

  return true;
}

// Performance testing function
template<typename NMSFunc>
double benchmark_nms(NMSFunc nms_func, std::vector<Box> input_boxes, float iou_threshold,
                    int num_iterations = 100) {
  // Warm up
  for (int i = 0; i < 5; ++i) {
    std::vector<Box> input_copy = input_boxes;
    std::vector<Box> output;
    nms_func(input_copy, output, iou_threshold);
  }

  // Timing
  auto start = std::chrono::high_resolution_clock::now();
  for (int i = 0; i < num_iterations; ++i) {
    std::vector<Box> input_copy = input_boxes;
    std::vector<Box> output;
    nms_func(input_copy, output, iou_threshold);
  }
  auto end = std::chrono::high_resolution_clock::now();

  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
  return duration.count() / 1000.0 / num_iterations; // ms per iteration
}

int main(int argc, char **argv) {
  // Parse command line arguments
  int num_boxes = 1000;
  float iou_threshold = 0.5f;

  if (argc >= 2) {
    num_boxes = atoi(argv[1]);
  }
  if (argc >= 3) {
    iou_threshold = atof(argv[2]);
  }

  printf("NMS Performance Test\n");
  printf("Number of boxes: %d\n", num_boxes);
  printf("IoU threshold: %.3f\n", iou_threshold);
  printf("=================================\n");

  // Generate test data
  printf("Generating %d random boxes...\n", num_boxes);
  std::vector<Box> input_boxes = generate_test_boxes(num_boxes);

  // Test CPU implementation
  printf("\nTesting CPU NMS...\n");
  std::vector<Box> input_cpu = input_boxes;
  std::vector<Box> output_cpu;

  auto cpu_start = std::chrono::high_resolution_clock::now();
  hard_nms_cpu(input_cpu, output_cpu, iou_threshold);
  auto cpu_end = std::chrono::high_resolution_clock::now();
  auto cpu_time = std::chrono::duration_cast<std::chrono::microseconds>(cpu_end - cpu_start);

  printf("CPU result: %zu boxes kept out of %d\n", output_cpu.size(), num_boxes);
  printf("CPU time: %.3f ms\n", cpu_time.count() / 1000.0);

  // Test GPU implementation
  printf("\nTesting GPU NMS...\n");
  std::vector<Box> input_gpu = input_boxes;
  std::vector<Box> output_gpu;

  auto gpu_start = std::chrono::high_resolution_clock::now();
  hard_nms_gpu(input_gpu, output_gpu, iou_threshold);
  auto gpu_end = std::chrono::high_resolution_clock::now();
  auto gpu_time = std::chrono::duration_cast<std::chrono::microseconds>(gpu_end - gpu_start);

  printf("GPU result: %zu boxes kept out of %d\n", output_gpu.size(), num_boxes);
  printf("GPU time: %.3f ms\n", gpu_time.count() / 1000.0);

  // Verify results
  printf("\nVerifying results...\n");
  bool passed = verify_results(output_cpu, output_gpu);
  printf("Verification: %s\n", passed ? "PASSED" : "FAILED");

  if (passed) {
    // Performance comparison
    printf("\nPerformance comparison:\n");
    printf("CPU vs GPU speedup: %.2fx\n",
           (cpu_time.count() / 1000.0) / (gpu_time.count() / 1000.0));

    // Detailed benchmarking for smaller datasets
    if (num_boxes <= 5000) {
      printf("\nDetailed benchmarking (100 iterations)...\n");

      double cpu_avg_time = benchmark_nms(hard_nms_cpu, input_boxes, iou_threshold);
      double gpu_avg_time = benchmark_nms(hard_nms_gpu, input_boxes, iou_threshold);

      printf("CPU average time: %.3f ms\n", cpu_avg_time);
      printf("GPU average time: %.3f ms\n", gpu_avg_time);
      printf("Average speedup: %.2fx\n", cpu_avg_time / gpu_avg_time);
    }
  }

  printf("\n=================================\n");
  printf("Overall result: %s\n", passed ? "PASSED" : "FAILED");

  return passed ? 0 : 1;
}