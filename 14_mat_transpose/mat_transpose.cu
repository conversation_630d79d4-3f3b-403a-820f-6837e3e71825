#include <algorithm>
#include <cuda_bf16.h>
#include <cuda_fp16.h>
#include <cuda_fp8.h>
#include <cuda_runtime.h>
#include <float.h>
#include <stdio.h>
#include <stdlib.h>
#include <vector>
#include <chrono>
#include <cmath>
#include <iostream>

#define checkCudaErrors(func)                                                  \
  {                                                                            \
    cudaError_t e = (func);                                                    \
    if (e != cudaSuccess)                                                      \
      printf("%s %d CUDA: %s\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
  }

#define WARP_SIZE 256
#define WARP_SIZE_S 16
#define PAD 1
#define INT4(value) (reinterpret_cast<int4 *>(&(value))[0])
#define FLOAT4(value) (reinterpret_cast<float4 *>(&(value))[0])
#define HALF2(value) (reinterpret_cast<half2 *>(&(value))[0])
#define BFLOAT2(value) (reinterpret_cast<__nv_bfloat162 *>(&(value))[0])
#define LDST128BITS(value) (reinterpret_cast<float4 *>(&(value))[0])
#define MAX_EXP_F32 88.3762626647949f
#define MIN_EXP_F32 -88.3762626647949f
#define MAX_EXP_F16 __float2half(11.089866488461016f)
#define MIN_EXP_F16 __float2half(-9.704060527839234f)

// FP32
// col2row means read x[row][col] and
// write y[col][row] row2col means read x[col][row] and write y[row][col]
__global__ void mat_transpose_f32_col2row_kernel(float *x, float *y,
                                                 const int row, const int col) {
  const int global_idx = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_row = global_idx / col;
  const int global_col = global_idx % col;
  if (global_idx < row * col) {
    y[global_col * row + global_row] = x[global_idx];
  }
}

__global__ void mat_transpose_f32_row2col_kernel(float *x, float *y,
                                                 const int row, const int col) {
  const int global_idx = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_col = global_idx / row;
  const int global_row = global_idx % row;
  if (global_idx < row * col) {
    y[global_idx] = x[global_row * col + global_col];
  }
}

__global__ void mat_transpose_f32x4_col2row_kernel(float *x, float *y,
                                                   const int row,
                                                   const int col) {
  int global_idx = blockIdx.x * blockDim.x + threadIdx.x;
  int global_col = (global_idx * 4) % col;
  int global_row = (global_idx * 4) / col;

  if (global_row < row && global_col + 3 < col) {
    float4 x_val = reinterpret_cast<float4 *>(x)[global_idx];

    y[global_col * row + global_row] = x_val.x;
    y[(global_col + 1) * row + global_row] = x_val.y;
    y[(global_col + 2) * row + global_row] = x_val.z;
    y[(global_col + 3) * row + global_row] = x_val.w;
  }
}
__global__ void mat_transpose_f32x4_row2col_kernel(float *x, float *y,
                                                   const int row,
                                                   const int col) {
  const int global_idx = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_col = (global_idx * 4) / row;
  const int global_row = (global_idx * 4) % row;

  if (global_row < row && global_col < col) {
    float4 x_val;
    x_val.x = x[global_row * col + global_col];
    x_val.y = x[(global_row + 1) * col + global_col];
    x_val.z = x[(global_row + 2) * col + global_col];
    x_val.w = x[(global_row + 3) * col + global_col];
    reinterpret_cast<float4 *>(y)[global_idx] = FLOAT4(x_val);
  }
}

// work for row == col
__global__ void mat_transpose_f32_diagonal2d_kernel(float *x, float *y, int row,
                                                    int col) {
  const int block_y = blockIdx.x;
  const int block_x = (blockIdx.x + blockIdx.y) % gridDim.x;
  const int global_col = threadIdx.x + blockDim.x * block_x;
  const int global_row = threadIdx.y + blockDim.y * block_y;
  if (global_col < col && global_row < row) {
    y[global_row * col + global_col] = x[global_col * row + global_row];
  }
}

__global__ void mat_transpose_f32_col2row2d_kernel(float *x, float *y,
                                                   const int row,
                                                   const int col) {
  const int global_x = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_y = blockIdx.y * blockDim.y + threadIdx.y;
  if (global_x < col && global_y < row) {
    y[global_x * row + global_y] = x[global_y * col + global_x];
  }
}

__global__ void mat_transpose_f32_row2col2d_kernel(float *x, float *y,
                                                   const int row,
                                                   const int col) {
  const int global_y = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_x = blockIdx.y * blockDim.y + threadIdx.y;
  if (global_y < col && global_x < row) {
    y[global_y * row + global_x] = x[global_x * col + global_y];
  }
}

__global__ void mat_transpose_f32x4_col2row2d_kernel(float *x, float *y,
                                                     const int row,
                                                     const int col) {
  const int global_x = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_y = blockIdx.y * blockDim.y + threadIdx.y;
  if (global_x * 4 + 3 < col && global_y < row) {
    float4 x_val = reinterpret_cast<float4 *>(x)[global_y * col / 4 + global_x];
    y[(global_x * 4) * row + global_y] = x_val.x;
    y[(global_x * 4 + 1) * row + global_y] = x_val.y;
    y[(global_x * 4 + 2) * row + global_y] = x_val.z;
    y[(global_x * 4 + 3) * row + global_y] = x_val.w;
  }
}
__global__ void mat_transpose_f32x4_row2col2d_kernel(float *x, float *y,
                                                     const int row,
                                                     const int col) {
  const int global_x = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_y = blockIdx.y * blockDim.y + threadIdx.y;
  if (global_y * 4 + 3 < row && global_x < col) {
    float4 x_val;
    x_val.x = x[(global_y * 4) * col + global_x];
    x_val.y = x[(global_y * 4 + 1) * col + global_x];
    x_val.z = x[(global_y * 4 + 2) * col + global_x];
    x_val.w = x[(global_y * 4 + 3) * col + global_x];
    reinterpret_cast<float4 *>(y)[global_x * row / 4 + global_y] =
        FLOAT4(x_val);
  }
}

__global__ void mat_transpose_f32x4_shared_col2row2d_kernel(float *x, float *y,
                                                            const int row,
                                                            const int col) {
  const int global_x = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_y = blockIdx.y * blockDim.y + threadIdx.y;
  const int local_x = threadIdx.x;
  const int local_y = threadIdx.y;
  __shared__ float tile[WARP_SIZE_S][WARP_SIZE_S * 4];
  if (global_x * 4 + 3 < col + 3 && global_y < row) {
    // load value from x to shared memory
    float4 x_val = reinterpret_cast<float4 *>(x)[global_y * col / 4 + global_x];
    FLOAT4(tile[local_y][local_x * 4]) = FLOAT4(x_val);
    __syncthreads();
    float4 smem_val;
    // load value from shared memory to y.
    // add STRIDE to satisfied different block size.
    constexpr int STRIDE = WARP_SIZE_S / 4;
    smem_val.x = tile[(local_y % STRIDE) * 4][local_x * 4 + local_y / STRIDE];
    smem_val.y =
        tile[(local_y % STRIDE) * 4 + 1][local_x * 4 + local_y / STRIDE];
    smem_val.z =
        tile[(local_y % STRIDE) * 4 + 2][local_x * 4 + local_y / STRIDE];
    smem_val.w =
        tile[(local_y % STRIDE) * 4 + 3][local_x * 4 + local_y / STRIDE];
    // map index n*n to (n/4)*(n*4)
    const int bid_y = blockIdx.y * blockDim.y;
    const int out_y = global_x * 4 + local_y / STRIDE;
    const int out_x = (local_y % STRIDE) * 4 + bid_y;
    reinterpret_cast<float4 *>(y)[(out_y * row + out_x) / 4] = FLOAT4(smem_val);
  }
}

__global__ void mat_transpose_f32x4_shared_row2col2d_kernel(float *x, float *y,
                                                            const int row,
                                                            const int col) {
  const int global_x = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_y = blockIdx.y * blockDim.y + threadIdx.y;
  const int local_x = threadIdx.x;
  const int local_y = threadIdx.y;
  __shared__ float tile[WARP_SIZE_S * 4][WARP_SIZE_S];
  if (global_y * 4 < row && global_x < col) {
    // load value from x to shared memory
    float4 x_val;
    x_val.x = x[(global_y * 4) * col + global_x];
    x_val.y = x[(global_y * 4 + 1) * col + global_x];
    x_val.z = x[(global_y * 4 + 2) * col + global_x];
    x_val.w = x[(global_y * 4 + 3) * col + global_x];
    tile[local_y * 4][local_x] = x_val.x;
    tile[local_y * 4 + 1][local_x] = x_val.y;
    tile[local_y * 4 + 2][local_x] = x_val.z;
    tile[local_y * 4 + 3][local_x] = x_val.w;
    __syncthreads();
    float4 smem_val;
    // load value from shared memory to y.
    // add STRIDE to satisfied different block size.
    // map index n*n to (n/4)*(n*4)
    constexpr int STRIDE = WARP_SIZE_S / 4;
    smem_val.x = tile[local_x * 4 + local_y / STRIDE][(local_y % STRIDE) * 4];
    smem_val.y =
        tile[local_x * 4 + local_y / STRIDE][(local_y % STRIDE) * 4 + 1];
    smem_val.z =
        tile[local_x * 4 + local_y / STRIDE][(local_y % STRIDE) * 4 + 2];
    smem_val.w =
        tile[local_x * 4 + local_y / STRIDE][(local_y % STRIDE) * 4 + 3];
    const int bid_x = blockIdx.x * blockDim.x;
    const int bid_y = blockIdx.y * blockDim.y;

    const int out_y = bid_x + (local_y % STRIDE) * 4;
    const int out_x = bid_y * 4 + local_x * 4 + (local_y / STRIDE);
    y[out_y * row + out_x] = smem_val.x;
    y[(out_y + 1) * row + out_x] = smem_val.y;
    y[(out_y + 2) * row + out_x] = smem_val.z;
    y[(out_y + 3) * row + out_x] = smem_val.w;
  }
}

__global__ void mat_transpose_f32x4_shared_bcf_col2row2d_kernel(float *x,
                                                                float *y,
                                                                const int row,
                                                                const int col) {
  const int global_x = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_y = blockIdx.y * blockDim.y + threadIdx.y;
  const int local_x = threadIdx.x;
  const int local_y = threadIdx.y;
  __shared__ float tile[WARP_SIZE_S][WARP_SIZE_S * 4 + PAD];
  if (global_x * 4 + 3 < col + 3 && global_y < row) {
    // load value from x to shared memory
    float4 x_val = reinterpret_cast<float4 *>(x)[global_y * col / 4 + global_x];
    tile[local_y][local_x * 4] = x_val.x;
    tile[local_y][local_x * 4 + 1] = x_val.y;
    tile[local_y][local_x * 4 + 2] = x_val.z;
    tile[local_y][local_x * 4 + 3] = x_val.w;
    __syncthreads();
    float4 smem_val;
    // load value from shared memory to y.
    // add STRIDE to satisfied different block size.
    constexpr int STRIDE = WARP_SIZE_S / 4;
    smem_val.x = tile[(local_y % STRIDE) * 4][local_x * 4 + local_y / STRIDE];
    smem_val.y =
        tile[(local_y % STRIDE) * 4 + 1][local_x * 4 + local_y / STRIDE];
    smem_val.z =
        tile[(local_y % STRIDE) * 4 + 2][local_x * 4 + local_y / STRIDE];
    smem_val.w =
        tile[(local_y % STRIDE) * 4 + 3][local_x * 4 + local_y / STRIDE];
    // map index n*n to (n/4)*(n*4)
    const int bid_y = blockIdx.y * blockDim.y;
    const int out_y = global_x * 4 + local_y / STRIDE;
    const int out_x = (local_y % STRIDE) * 4 + bid_y;
    reinterpret_cast<float4 *>(y)[(out_y * row + out_x) / 4] = FLOAT4(smem_val);
  }
}

__global__ void mat_transpose_f32x4_shared_bcf_row2col2d_kernel(float *x,
                                                                float *y,
                                                                const int row,
                                                                const int col) {
  const int global_x = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_y = blockIdx.y * blockDim.y + threadIdx.y;
  const int local_x = threadIdx.x;
  const int local_y = threadIdx.y;
  __shared__ float tile[WARP_SIZE_S * 4][WARP_SIZE_S + PAD];
  if (global_y * 4 < row && global_x < col) {
    // load value from x to shared memory
    float4 x_val;
    x_val.x = x[(global_y * 4) * col + global_x];
    x_val.y = x[(global_y * 4 + 1) * col + global_x];
    x_val.z = x[(global_y * 4 + 2) * col + global_x];
    x_val.w = x[(global_y * 4 + 3) * col + global_x];
    tile[local_y * 4][local_x] = x_val.x;
    tile[local_y * 4 + 1][local_x] = x_val.y;
    tile[local_y * 4 + 2][local_x] = x_val.z;
    tile[local_y * 4 + 3][local_x] = x_val.w;
    __syncthreads();
    float4 smem_val;
    // load value from shared memory to y.
    // add STRIDE to satisfied different block size.
    // map index n*n to (n/4)*(n*4)
    constexpr int STRIDE = WARP_SIZE_S / 4;
    smem_val.x = tile[local_x * 4 + local_y / STRIDE][(local_y % STRIDE) * 4];
    smem_val.y =
        tile[local_x * 4 + local_y / STRIDE][(local_y % STRIDE) * 4 + 1];
    smem_val.z =
        tile[local_x * 4 + local_y / STRIDE][(local_y % STRIDE) * 4 + 2];
    smem_val.w =
        tile[local_x * 4 + local_y / STRIDE][(local_y % STRIDE) * 4 + 3];
    const int bid_x = blockIdx.x * blockDim.x;
    const int bid_y = blockIdx.y * blockDim.y;

    const int out_y = bid_x + (local_y % STRIDE) * 4;
    const int out_x = bid_y * 4 + local_x * 4 + (local_y / STRIDE);
    y[out_y * row + out_x] = smem_val.x;
    y[(out_y + 1) * row + out_x] = smem_val.y;
    y[(out_y + 2) * row + out_x] = smem_val.z;
    y[(out_y + 3) * row + out_x] = smem_val.w;
  }
}

__global__ void mat_transpose_f32x4_shared_bcf_merge_write_row2col2d_kernel(
    float *x, float *y, const int row, const int col) {
  const int global_x = blockIdx.x * blockDim.x + threadIdx.x;
  const int global_y = blockIdx.y * blockDim.y + threadIdx.y;
  const int local_x = threadIdx.x;
  const int local_y = threadIdx.y;
  __shared__ float tile[WARP_SIZE_S * 4][WARP_SIZE_S + PAD];
  if (global_y * 4 < row && global_x < col) {
    // load value from x to shared memory
    float4 x_val;
    x_val.x = x[(global_y * 4) * col + global_x];
    x_val.y = x[(global_y * 4 + 1) * col + global_x];
    x_val.z = x[(global_y * 4 + 2) * col + global_x];
    x_val.w = x[(global_y * 4 + 3) * col + global_x];
    tile[local_y * 4][local_x] = x_val.x;
    tile[local_y * 4 + 1][local_x] = x_val.y;
    tile[local_y * 4 + 2][local_x] = x_val.z;
    tile[local_y * 4 + 3][local_x] = x_val.w;
    __syncthreads();
    float4 smem_val;
    // load value from shared memory to y.
    smem_val.x = tile[local_x * 4][local_y];
    smem_val.y = tile[local_x * 4 + 1][local_y];
    smem_val.z = tile[local_x * 4 + 2][local_y];
    smem_val.w = tile[local_x * 4 + 3][local_y];

    const int gid_x = blockIdx.x * blockDim.x;
    const int gid_y = blockIdx.y * blockDim.y * 4;
    const int out_y = gid_y + local_x * 4;
    const int out_x = gid_x + local_y;
    reinterpret_cast<float4 *>(y)[(out_x * row + out_y) / 4] = FLOAT4(smem_val);
  }
}

// CPU reference implementation for verification
void mat_transpose_cpu(float *input, float *output, int rows, int cols) {
  for (int i = 0; i < rows; i++) {
    for (int j = 0; j < cols; j++) {
      output[j * rows + i] = input[i * cols + j];
    }
  }
}

// Initialize matrix with test data
void init_matrix(float *matrix, int rows, int cols) {
  for (int i = 0; i < rows * cols; i++) {
    matrix[i] = static_cast<float>(i % 100) + 0.5f;
  }
}

// Verify results
bool verify_result(float *gpu_result, float *cpu_result, int size, float tolerance = 1e-5f) {
  int failed_count = 0;
  for (int i = 0; i < size; i++) {
    float diff = std::abs(gpu_result[i] - cpu_result[i]);
    if (diff > tolerance) {
      if (failed_count < 5) { // Only print first 5 failures
        printf("Verification failed at index %d: GPU=%.6f, CPU=%.6f, diff=%.6e\n",
               i, gpu_result[i], cpu_result[i], diff);
      }
      failed_count++;
    }
  }

  if (failed_count > 0) {
    printf("Total failed elements: %d/%d\n", failed_count, size);
    return false;
  }
  return true;
}

// Performance testing function
template<typename KernelFunc>
double benchmark_kernel(KernelFunc kernel_func, float *d_input, float *d_output,
                       int rows, int cols, int num_iterations = 1000) {
  // Warm up
  for (int i = 0; i < 10; i++) {
    kernel_func(d_input, d_output, rows, cols);
  }
  checkCudaErrors(cudaDeviceSynchronize());

  // Timing
  auto start = std::chrono::high_resolution_clock::now();
  for (int i = 0; i < num_iterations; i++) {
    kernel_func(d_input, d_output, rows, cols);
  }
  checkCudaErrors(cudaDeviceSynchronize());
  auto end = std::chrono::high_resolution_clock::now();

  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
  return duration.count() / 1000.0 / num_iterations; // ms per iteration
}

int main(int argc, char **argv) {
  // Parse command line arguments
  int rows = 1024, cols = 1024;
  if (argc >= 3) {
    rows = atoi(argv[1]);
    cols = atoi(argv[2]);
  }

  printf("Matrix Transpose Performance Test\n");
  printf("Matrix size: %d x %d\n", rows, cols);
  printf("=================================\n");

  // Allocate host memory
  size_t size = rows * cols * sizeof(float);
  float *h_input = (float*)malloc(size);
  float *h_output_gpu = (float*)malloc(size);
  float *h_output_cpu = (float*)malloc(size);

  // Initialize input matrix
  init_matrix(h_input, rows, cols);

  // Allocate device memory
  float *d_input, *d_output;
  checkCudaErrors(cudaMalloc(&d_input, size));
  checkCudaErrors(cudaMalloc(&d_output, size));

  // Copy input data to device
  checkCudaErrors(cudaMemcpy(d_input, h_input, size, cudaMemcpyHostToDevice));

  // Compute CPU reference
  printf("Computing CPU reference...\n");
  auto cpu_start = std::chrono::high_resolution_clock::now();
  mat_transpose_cpu(h_input, h_output_cpu, rows, cols);
  auto cpu_end = std::chrono::high_resolution_clock::now();
  auto cpu_time = std::chrono::duration_cast<std::chrono::microseconds>(cpu_end - cpu_start);
  printf("CPU time: %.3f ms\n\n", cpu_time.count() / 1000.0);

  bool all_passed = true;

  // Test 1: Basic col2row kernel
  printf("Testing basic col2row kernel...\n");
  {
    dim3 block(256);
    dim3 grid((rows * cols + block.x - 1) / block.x);

    checkCudaErrors(cudaMemset(d_output, 0, size));
    mat_transpose_f32_col2row_kernel<<<grid, block>>>(d_input, d_output, rows, cols);
    checkCudaErrors(cudaGetLastError());
    checkCudaErrors(cudaDeviceSynchronize());

    checkCudaErrors(cudaMemcpy(h_output_gpu, d_output, size, cudaMemcpyDeviceToHost));
    bool passed = verify_result(h_output_gpu, h_output_cpu, rows * cols);
    printf("Basic col2row: %s\n", passed ? "PASSED" : "FAILED");
    all_passed &= passed;

    double time_ms = benchmark_kernel(
        [&](float *input, float *output, int r, int c) {
          mat_transpose_f32_col2row_kernel<<<grid, block>>>(input, output, r, c);
        },
        d_input, d_output, rows, cols);

    double bandwidth = (2.0 * size) / (time_ms * 1e-3) / 1e9; // GB/s
    printf("Basic col2row performance: %.3f ms, %.2f GB/s\n\n", time_ms, bandwidth);
  }

  // Test 2: Vectorized float4 col2row kernel (only if cols is divisible by 4)
  if (cols % 4 == 0) {
    printf("Testing float4 col2row kernel...\n");
    dim3 block(256);
    dim3 grid((rows * cols / 4 + block.x - 1) / block.x);

    checkCudaErrors(cudaMemset(d_output, 0, size));
    mat_transpose_f32x4_col2row_kernel<<<grid, block>>>(d_input, d_output, rows, cols);
    checkCudaErrors(cudaGetLastError());
    checkCudaErrors(cudaDeviceSynchronize());

    checkCudaErrors(cudaMemcpy(h_output_gpu, d_output, size, cudaMemcpyDeviceToHost));
    bool passed = verify_result(h_output_gpu, h_output_cpu, rows * cols);
    printf("Float4 col2row: %s\n", passed ? "PASSED" : "FAILED");
    all_passed &= passed;

    double time_ms = benchmark_kernel(
        [&](float *input, float *output, int r, int c) {
          mat_transpose_f32x4_col2row_kernel<<<grid, block>>>(input, output, r, c);
        },
        d_input, d_output, rows, cols);

    double bandwidth = (2.0 * size) / (time_ms * 1e-3) / 1e9; // GB/s
    printf("Float4 col2row performance: %.3f ms, %.2f GB/s\n\n", time_ms, bandwidth);
  }

  // Test 3: 2D grid col2row kernel
  printf("Testing 2D grid col2row kernel...\n");
  {
    dim3 block(16, 16);
    dim3 grid((cols + block.x - 1) / block.x, (rows + block.y - 1) / block.y);

    checkCudaErrors(cudaMemset(d_output, 0, size));
    mat_transpose_f32_col2row2d_kernel<<<grid, block>>>(d_input, d_output, rows, cols);
    checkCudaErrors(cudaGetLastError());
    checkCudaErrors(cudaDeviceSynchronize());

    checkCudaErrors(cudaMemcpy(h_output_gpu, d_output, size, cudaMemcpyDeviceToHost));
    bool passed = verify_result(h_output_gpu, h_output_cpu, rows * cols);
    printf("2D grid col2row: %s\n", passed ? "PASSED" : "FAILED");
    all_passed &= passed;

    double time_ms = benchmark_kernel(
        [&](float *input, float *output, int r, int c) {
          mat_transpose_f32_col2row2d_kernel<<<grid, block>>>(input, output, r, c);
        },
        d_input, d_output, rows, cols);

    double bandwidth = (2.0 * size) / (time_ms * 1e-3) / 1e9; // GB/s
    printf("2D grid col2row performance: %.3f ms, %.2f GB/s\n\n", time_ms, bandwidth);
  }

  // Test 4: Shared memory optimized kernel (only if both dimensions are divisible by 4)
  if (rows % 4 == 0 && cols % 4 == 0) {
    printf("Testing shared memory optimized kernel...\n");
    dim3 block(WARP_SIZE_S, WARP_SIZE_S);
    dim3 grid((cols / 4 + block.x - 1) / block.x, (rows + block.y - 1) / block.y);

    checkCudaErrors(cudaMemset(d_output, 0, size));
    mat_transpose_f32x4_shared_col2row2d_kernel<<<grid, block>>>(d_input, d_output, rows, cols);
    checkCudaErrors(cudaGetLastError());
    checkCudaErrors(cudaDeviceSynchronize());

    checkCudaErrors(cudaMemcpy(h_output_gpu, d_output, size, cudaMemcpyDeviceToHost));
    bool passed = verify_result(h_output_gpu, h_output_cpu, rows * cols);
    printf("Shared memory optimized: %s\n", passed ? "PASSED" : "FAILED");
    all_passed &= passed;

    double time_ms = benchmark_kernel(
        [&](float *input, float *output, int r, int c) {
          mat_transpose_f32x4_shared_col2row2d_kernel<<<grid, block>>>(input, output, r, c);
        },
        d_input, d_output, rows, cols);

    double bandwidth = (2.0 * size) / (time_ms * 1e-3) / 1e9; // GB/s
    printf("Shared memory optimized performance: %.3f ms, %.2f GB/s\n\n", time_ms, bandwidth);
  }

  // Test 5: Bank conflict free kernel (only if both dimensions are divisible by 4)
  if (rows % 4 == 0 && cols % 4 == 0) {
    printf("Testing bank conflict free kernel...\n");
    dim3 block(WARP_SIZE_S, WARP_SIZE_S);
    dim3 grid((cols / 4 + block.x - 1) / block.x, (rows + block.y - 1) / block.y);

    checkCudaErrors(cudaMemset(d_output, 0, size));
    mat_transpose_f32x4_shared_bcf_col2row2d_kernel<<<grid, block>>>(d_input, d_output, rows, cols);
    checkCudaErrors(cudaGetLastError());
    checkCudaErrors(cudaDeviceSynchronize());

    checkCudaErrors(cudaMemcpy(h_output_gpu, d_output, size, cudaMemcpyDeviceToHost));
    bool passed = verify_result(h_output_gpu, h_output_cpu, rows * cols);
    printf("Bank conflict free: %s\n", passed ? "PASSED" : "FAILED");
    all_passed &= passed;

    double time_ms = benchmark_kernel(
        [&](float *input, float *output, int r, int c) {
          mat_transpose_f32x4_shared_bcf_col2row2d_kernel<<<grid, block>>>(input, output, r, c);
        },
        d_input, d_output, rows, cols);

    double bandwidth = (2.0 * size) / (time_ms * 1e-3) / 1e9; // GB/s
    printf("Bank conflict free performance: %.3f ms, %.2f GB/s\n\n", time_ms, bandwidth);
  }

  // Summary
  printf("=================================\n");
  printf("Overall result: %s\n", all_passed ? "ALL TESTS PASSED" : "SOME TESTS FAILED");

  // Cleanup
  free(h_input);
  free(h_output_gpu);
  free(h_output_cpu);
  checkCudaErrors(cudaFree(d_input));
  checkCudaErrors(cudaFree(d_output));

  return all_passed ? 0 : 1;
}