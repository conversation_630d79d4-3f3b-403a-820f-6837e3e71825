set(CUTLASS_PATH "/mnt/g/project/CUDA/cutlass/include")
set(CUTLASS_UTIL_PATH "/mnt/g/project/CUDA/cutlass/tools/util/include")
set(CMAKE_CUDA_ARCHITECTURES 89)
set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} --expt-relaxed-constexpr -std=c++17")

# 显式设置本项目使用C++14标准（覆盖最外层的11）
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)  # 强制要求C++标准

# 添加当前目录变量
set(CURRENT_DIR ${CMAKE_CURRENT_SOURCE_DIR})

# add_executable(v1_print_half vl_print_half.cu)
# target_link_libraries(v1_print_half PRIVATE CUDA::cudart ${CUDA_cublas_LIBRARY})
# target_include_directories(v1_print_half PRIVATE ${CUTLASS_PATH})
# if(CMAKE_BUILD_TYPE STREQUAL "Debug")
#     target_compile_options(v1_print_half PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:$<$<CONFIG:Debug>:-O0 -G>>)
# else()
#     target_compile_options(v1_print_half PRIVATE -lineinfo)
# endif()


# add_executable(v2_gemm_kernel v2_gemm_kernel.cu)
# target_link_libraries(v2_gemm_kernel PRIVATE CUDA::cudart ${CUDA_cublas_LIBRARY})
# target_include_directories(v2_gemm_kernel PRIVATE ${CUTLASS_PATH} ${CUTLASS_UTIL_PATH})
# if(CMAKE_BUILD_TYPE STREQUAL "Debug")
#     target_compile_options(v2_gemm_kernel PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:$<$<CONFIG:Debug>:-O0 -G>>)
# else()
#     target_compile_options(v2_gemm_kernel PRIVATE -lineinfo)
# endif()


add_executable(v1_basic_gemm v1_basic_gemm.cu)

target_link_libraries(v1_basic_gemm PRIVATE CUDA::cudart ${CUDA_cublas_LIBRARY})
target_include_directories(v1_basic_gemm PRIVATE ${CUTLASS_PATH} ${CUTLASS_UTIL_PATH} ${CURRENT_DIR}/common)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(v1_basic_gemm PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:$<$<CONFIG:Debug>:-O0 -G>>)
else()
    target_compile_options(v1_basic_gemm PRIVATE -lineinfo)
endif()

add_executable(v3_turing_tensorop_gemm v3_turing_tensorop_gemm.cu)

# 为这个目标单独设置标准（双重保险）
set_target_properties(v3_turing_tensorop_gemm PROPERTIES
    CUDA_STANDARD 17
    CUDA_STANDARD_REQUIRED ON
)

target_link_libraries(v3_turing_tensorop_gemm PRIVATE CUDA::cudart ${CUDA_cublas_LIBRARY})
target_include_directories(v3_turing_tensorop_gemm PRIVATE ${CUTLASS_PATH} ${CUTLASS_UTIL_PATH} ${CURRENT_DIR}/common)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(v3_turing_tensorop_gemm PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:$<$<CONFIG:Debug>:-O0 -G>>)
else()
    target_compile_options(v3_turing_tensorop_gemm PRIVATE -lineinfo)
endif()
