cmake_minimum_required(VERSION 3.18)
project(dot_product LANGUAGES CXX CUDA)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set CUDA standard
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

# Find required packages
find_package(CUDAToolkit REQUIRED)

# Set CUDA architecture (adjust based on your GPU)
set(CMAKE_CUDA_ARCHITECTURES 75 80 86 89)

# Create executables for both versions
add_executable(dot_product_v0 dot_product_v0.cu)
add_executable(dot_product_v1 dot_product_v1.cu)

# Set CUDA properties for both targets
set_target_properties(dot_product_v0 dot_product_v1 PROPERTIES
    CUDA_SEPARABLE_COMPILATION ON
    CUDA_RESOLVE_DEVICE_SYMBOLS ON
)

# Link CUDA libraries
target_link_libraries(dot_product_v0 PRIVATE CUDA::cudart)
target_link_libraries(dot_product_v1 PRIVATE CUDA::cudart)

# Compiler-specific options
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(dot_product_v0 PRIVATE 
        $<$<COMPILE_LANGUAGE:CUDA>:-G -g>
        $<$<COMPILE_LANGUAGE:CXX>:-g>
    )
    target_compile_options(dot_product_v1 PRIVATE 
        $<$<COMPILE_LANGUAGE:CUDA>:-G -g>
        $<$<COMPILE_LANGUAGE:CXX>:-g>
    )
else()
    target_compile_options(dot_product_v0 PRIVATE 
        $<$<COMPILE_LANGUAGE:CUDA>:-O3>
        $<$<COMPILE_LANGUAGE:CXX>:-O3>
    )
    target_compile_options(dot_product_v1 PRIVATE 
        $<$<COMPILE_LANGUAGE:CUDA>:-O3>
        $<$<COMPILE_LANGUAGE:CXX>:-O3>
    )
endif()

# Print build information
message(STATUS "CUDA Toolkit Version: ${CUDAToolkit_VERSION}")
message(STATUS "CUDA Architectures: ${CMAKE_CUDA_ARCHITECTURES}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")

# Optional: Add install targets
install(TARGETS dot_product_v0 dot_product_v1
    RUNTIME DESTINATION bin
)

# Optional: Add a custom target to build both versions
add_custom_target(dot_product_all
    DEPENDS dot_product_v0 dot_product_v1
    COMMENT "Building all dot product versions"
)
