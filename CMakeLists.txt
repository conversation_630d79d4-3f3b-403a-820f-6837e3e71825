cmake_minimum_required(VERSION 3.20.0)
project(cuda_practice VERSION 0.1.0 LANGUAGES CUDA C CXX)
set(CMAKE_CUDA_ARCHITECTURES 89)
find_package(CUDAToolkit)
# set(CMAKE_CUDA_STANDARD 11)
set(CMAKE_CUDA_STANDARD 17)

# add_subdirectory(1_cuda_test)
# add_subdirectory(2_elementwise)
# add_subdirectory(3_reduce)
add_subdirectory(4_sgemm)
# add_subdirectory(5_sgemv)
# add_subdirectory(9_optimize_gemm)
# add_subdirectory(10_hgemm)
# add_subdirectory(11_cutlass_study)
# add_subdirectory(22_histogram)
# add_subdirectory(17_sigmoid)
# add_subdirectory(14_mat_transpose)
# add_subdirectory(12_spmv)
# add_subdirectory(13_spmm)
# add_subdirectory(14_mat_transpose)
# add_subdirectory(15_dot-product)
# add_subdirectory(16_softmax)
# add_subdirectory(18_relu)
# add_subdirectory(19_layer_norm)
# add_subdirectory(20_rms_norm)
# add_subdirectory(21_nms)