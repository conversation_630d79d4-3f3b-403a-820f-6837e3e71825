# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles /mnt/g/project/CUDA/cuda_practice/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named gemm_common

# Build rule for target.
gemm_common: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gemm_common
.PHONY : gemm_common

# fast build rule for target.
gemm_common/fast:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/build
.PHONY : gemm_common/fast

#=============================================================================
# Target rules for targets named sgemm_v0_naive

# Build rule for target.
sgemm_v0_naive: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sgemm_v0_naive
.PHONY : sgemm_v0_naive

# fast build rule for target.
sgemm_v0_naive/fast:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build.make 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build
.PHONY : sgemm_v0_naive/fast

#=============================================================================
# Target rules for targets named sgemm_v1

# Build rule for target.
sgemm_v1: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sgemm_v1
.PHONY : sgemm_v1

# fast build rule for target.
sgemm_v1/fast:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v1.dir/build.make 4_sgemm/CMakeFiles/sgemm_v1.dir/build
.PHONY : sgemm_v1/fast

#=============================================================================
# Target rules for targets named sgemm_v2

# Build rule for target.
sgemm_v2: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sgemm_v2
.PHONY : sgemm_v2

# fast build rule for target.
sgemm_v2/fast:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v2.dir/build.make 4_sgemm/CMakeFiles/sgemm_v2.dir/build
.PHONY : sgemm_v2/fast

#=============================================================================
# Target rules for targets named sgemm_v3

# Build rule for target.
sgemm_v3: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sgemm_v3
.PHONY : sgemm_v3

# fast build rule for target.
sgemm_v3/fast:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v3.dir/build.make 4_sgemm/CMakeFiles/sgemm_v3.dir/build
.PHONY : sgemm_v3/fast

#=============================================================================
# Target rules for targets named sgemm_v4

# Build rule for target.
sgemm_v4: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sgemm_v4
.PHONY : sgemm_v4

# fast build rule for target.
sgemm_v4/fast:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v4.dir/build.make 4_sgemm/CMakeFiles/sgemm_v4.dir/build
.PHONY : sgemm_v4/fast

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... gemm_common"
	@echo "... sgemm_v0_naive"
	@echo "... sgemm_v1"
	@echo "... sgemm_v2"
	@echo "... sgemm_v3"
	@echo "... sgemm_v4"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

