# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles /mnt/g/project/CUDA/cuda_practice/build/4_sgemm//CMakeFiles/progress.marks
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
4_sgemm/CMakeFiles/gemm_common.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/gemm_common.dir/rule
.PHONY : 4_sgemm/CMakeFiles/gemm_common.dir/rule

# Convenience name for target.
gemm_common: 4_sgemm/CMakeFiles/gemm_common.dir/rule
.PHONY : gemm_common

# fast build rule for target.
gemm_common/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/build
.PHONY : gemm_common/fast

# Convenience name for target.
4_sgemm/CMakeFiles/sgemm_v0_naive.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/rule
.PHONY : 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/rule

# Convenience name for target.
sgemm_v0_naive: 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/rule
.PHONY : sgemm_v0_naive

# fast build rule for target.
sgemm_v0_naive/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build.make 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build
.PHONY : sgemm_v0_naive/fast

# Convenience name for target.
4_sgemm/CMakeFiles/sgemm_v1.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/sgemm_v1.dir/rule
.PHONY : 4_sgemm/CMakeFiles/sgemm_v1.dir/rule

# Convenience name for target.
sgemm_v1: 4_sgemm/CMakeFiles/sgemm_v1.dir/rule
.PHONY : sgemm_v1

# fast build rule for target.
sgemm_v1/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v1.dir/build.make 4_sgemm/CMakeFiles/sgemm_v1.dir/build
.PHONY : sgemm_v1/fast

# Convenience name for target.
4_sgemm/CMakeFiles/sgemm_v2.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/sgemm_v2.dir/rule
.PHONY : 4_sgemm/CMakeFiles/sgemm_v2.dir/rule

# Convenience name for target.
sgemm_v2: 4_sgemm/CMakeFiles/sgemm_v2.dir/rule
.PHONY : sgemm_v2

# fast build rule for target.
sgemm_v2/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v2.dir/build.make 4_sgemm/CMakeFiles/sgemm_v2.dir/build
.PHONY : sgemm_v2/fast

# Convenience name for target.
4_sgemm/CMakeFiles/sgemm_v3.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/sgemm_v3.dir/rule
.PHONY : 4_sgemm/CMakeFiles/sgemm_v3.dir/rule

# Convenience name for target.
sgemm_v3: 4_sgemm/CMakeFiles/sgemm_v3.dir/rule
.PHONY : sgemm_v3

# fast build rule for target.
sgemm_v3/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v3.dir/build.make 4_sgemm/CMakeFiles/sgemm_v3.dir/build
.PHONY : sgemm_v3/fast

# Convenience name for target.
4_sgemm/CMakeFiles/sgemm_v4.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/sgemm_v4.dir/rule
.PHONY : 4_sgemm/CMakeFiles/sgemm_v4.dir/rule

# Convenience name for target.
sgemm_v4: 4_sgemm/CMakeFiles/sgemm_v4.dir/rule
.PHONY : sgemm_v4

# fast build rule for target.
sgemm_v4/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v4.dir/build.make 4_sgemm/CMakeFiles/sgemm_v4.dir/build
.PHONY : sgemm_v4/fast

common/REF_MMult.o: common/REF_MMult.cpp.o
.PHONY : common/REF_MMult.o

# target to build an object file
common/REF_MMult.cpp.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o
.PHONY : common/REF_MMult.cpp.o

common/REF_MMult.i: common/REF_MMult.cpp.i
.PHONY : common/REF_MMult.i

# target to preprocess a source file
common/REF_MMult.cpp.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.i
.PHONY : common/REF_MMult.cpp.i

common/REF_MMult.s: common/REF_MMult.cpp.s
.PHONY : common/REF_MMult.s

# target to generate assembly for a file
common/REF_MMult.cpp.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.s
.PHONY : common/REF_MMult.cpp.s

common/compare_matrices.o: common/compare_matrices.cpp.o
.PHONY : common/compare_matrices.o

# target to build an object file
common/compare_matrices.cpp.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o
.PHONY : common/compare_matrices.cpp.o

common/compare_matrices.i: common/compare_matrices.cpp.i
.PHONY : common/compare_matrices.i

# target to preprocess a source file
common/compare_matrices.cpp.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.i
.PHONY : common/compare_matrices.cpp.i

common/compare_matrices.s: common/compare_matrices.cpp.s
.PHONY : common/compare_matrices.s

# target to generate assembly for a file
common/compare_matrices.cpp.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.s
.PHONY : common/compare_matrices.cpp.s

common/copy_matrix.o: common/copy_matrix.cpp.o
.PHONY : common/copy_matrix.o

# target to build an object file
common/copy_matrix.cpp.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o
.PHONY : common/copy_matrix.cpp.o

common/copy_matrix.i: common/copy_matrix.cpp.i
.PHONY : common/copy_matrix.i

# target to preprocess a source file
common/copy_matrix.cpp.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.i
.PHONY : common/copy_matrix.cpp.i

common/copy_matrix.s: common/copy_matrix.cpp.s
.PHONY : common/copy_matrix.s

# target to generate assembly for a file
common/copy_matrix.cpp.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.s
.PHONY : common/copy_matrix.cpp.s

common/cpu_sgemm.o: common/cpu_sgemm.cpp.o
.PHONY : common/cpu_sgemm.o

# target to build an object file
common/cpu_sgemm.cpp.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o
.PHONY : common/cpu_sgemm.cpp.o

common/cpu_sgemm.i: common/cpu_sgemm.cpp.i
.PHONY : common/cpu_sgemm.i

# target to preprocess a source file
common/cpu_sgemm.cpp.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.i
.PHONY : common/cpu_sgemm.cpp.i

common/cpu_sgemm.s: common/cpu_sgemm.cpp.s
.PHONY : common/cpu_sgemm.s

# target to generate assembly for a file
common/cpu_sgemm.cpp.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.s
.PHONY : common/cpu_sgemm.cpp.s

common/dclock.o: common/dclock.cpp.o
.PHONY : common/dclock.o

# target to build an object file
common/dclock.cpp.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.o
.PHONY : common/dclock.cpp.o

common/dclock.i: common/dclock.cpp.i
.PHONY : common/dclock.i

# target to preprocess a source file
common/dclock.cpp.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.i
.PHONY : common/dclock.cpp.i

common/dclock.s: common/dclock.cpp.s
.PHONY : common/dclock.s

# target to generate assembly for a file
common/dclock.cpp.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.s
.PHONY : common/dclock.cpp.s

common/random_matrix.o: common/random_matrix.cpp.o
.PHONY : common/random_matrix.o

# target to build an object file
common/random_matrix.cpp.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o
.PHONY : common/random_matrix.cpp.o

common/random_matrix.i: common/random_matrix.cpp.i
.PHONY : common/random_matrix.i

# target to preprocess a source file
common/random_matrix.cpp.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.i
.PHONY : common/random_matrix.cpp.i

common/random_matrix.s: common/random_matrix.cpp.s
.PHONY : common/random_matrix.s

# target to generate assembly for a file
common/random_matrix.cpp.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.s
.PHONY : common/random_matrix.cpp.s

sgemm_v0_naive.o: sgemm_v0_naive.cu.o
.PHONY : sgemm_v0_naive.o

# target to build an object file
sgemm_v0_naive.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build.make 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/sgemm_v0_naive.cu.o
.PHONY : sgemm_v0_naive.cu.o

sgemm_v0_naive.i: sgemm_v0_naive.cu.i
.PHONY : sgemm_v0_naive.i

# target to preprocess a source file
sgemm_v0_naive.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build.make 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/sgemm_v0_naive.cu.i
.PHONY : sgemm_v0_naive.cu.i

sgemm_v0_naive.s: sgemm_v0_naive.cu.s
.PHONY : sgemm_v0_naive.s

# target to generate assembly for a file
sgemm_v0_naive.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build.make 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/sgemm_v0_naive.cu.s
.PHONY : sgemm_v0_naive.cu.s

sgemm_v1.o: sgemm_v1.cu.o
.PHONY : sgemm_v1.o

# target to build an object file
sgemm_v1.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v1.dir/build.make 4_sgemm/CMakeFiles/sgemm_v1.dir/sgemm_v1.cu.o
.PHONY : sgemm_v1.cu.o

sgemm_v1.i: sgemm_v1.cu.i
.PHONY : sgemm_v1.i

# target to preprocess a source file
sgemm_v1.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v1.dir/build.make 4_sgemm/CMakeFiles/sgemm_v1.dir/sgemm_v1.cu.i
.PHONY : sgemm_v1.cu.i

sgemm_v1.s: sgemm_v1.cu.s
.PHONY : sgemm_v1.s

# target to generate assembly for a file
sgemm_v1.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v1.dir/build.make 4_sgemm/CMakeFiles/sgemm_v1.dir/sgemm_v1.cu.s
.PHONY : sgemm_v1.cu.s

sgemm_v2.o: sgemm_v2.cu.o
.PHONY : sgemm_v2.o

# target to build an object file
sgemm_v2.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v2.dir/build.make 4_sgemm/CMakeFiles/sgemm_v2.dir/sgemm_v2.cu.o
.PHONY : sgemm_v2.cu.o

sgemm_v2.i: sgemm_v2.cu.i
.PHONY : sgemm_v2.i

# target to preprocess a source file
sgemm_v2.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v2.dir/build.make 4_sgemm/CMakeFiles/sgemm_v2.dir/sgemm_v2.cu.i
.PHONY : sgemm_v2.cu.i

sgemm_v2.s: sgemm_v2.cu.s
.PHONY : sgemm_v2.s

# target to generate assembly for a file
sgemm_v2.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v2.dir/build.make 4_sgemm/CMakeFiles/sgemm_v2.dir/sgemm_v2.cu.s
.PHONY : sgemm_v2.cu.s

sgemm_v3.o: sgemm_v3.cu.o
.PHONY : sgemm_v3.o

# target to build an object file
sgemm_v3.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v3.dir/build.make 4_sgemm/CMakeFiles/sgemm_v3.dir/sgemm_v3.cu.o
.PHONY : sgemm_v3.cu.o

sgemm_v3.i: sgemm_v3.cu.i
.PHONY : sgemm_v3.i

# target to preprocess a source file
sgemm_v3.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v3.dir/build.make 4_sgemm/CMakeFiles/sgemm_v3.dir/sgemm_v3.cu.i
.PHONY : sgemm_v3.cu.i

sgemm_v3.s: sgemm_v3.cu.s
.PHONY : sgemm_v3.s

# target to generate assembly for a file
sgemm_v3.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v3.dir/build.make 4_sgemm/CMakeFiles/sgemm_v3.dir/sgemm_v3.cu.s
.PHONY : sgemm_v3.cu.s

sgemm_v4.o: sgemm_v4.cu.o
.PHONY : sgemm_v4.o

# target to build an object file
sgemm_v4.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v4.dir/build.make 4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o
.PHONY : sgemm_v4.cu.o

sgemm_v4.i: sgemm_v4.cu.i
.PHONY : sgemm_v4.i

# target to preprocess a source file
sgemm_v4.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v4.dir/build.make 4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.i
.PHONY : sgemm_v4.cu.i

sgemm_v4.s: sgemm_v4.cu.s
.PHONY : sgemm_v4.s

# target to generate assembly for a file
sgemm_v4.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v4.dir/build.make 4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.s
.PHONY : sgemm_v4.cu.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... gemm_common"
	@echo "... sgemm_v0_naive"
	@echo "... sgemm_v1"
	@echo "... sgemm_v2"
	@echo "... sgemm_v3"
	@echo "... sgemm_v4"
	@echo "... common/REF_MMult.o"
	@echo "... common/REF_MMult.i"
	@echo "... common/REF_MMult.s"
	@echo "... common/compare_matrices.o"
	@echo "... common/compare_matrices.i"
	@echo "... common/compare_matrices.s"
	@echo "... common/copy_matrix.o"
	@echo "... common/copy_matrix.i"
	@echo "... common/copy_matrix.s"
	@echo "... common/cpu_sgemm.o"
	@echo "... common/cpu_sgemm.i"
	@echo "... common/cpu_sgemm.s"
	@echo "... common/dclock.o"
	@echo "... common/dclock.i"
	@echo "... common/dclock.s"
	@echo "... common/random_matrix.o"
	@echo "... common/random_matrix.i"
	@echo "... common/random_matrix.s"
	@echo "... sgemm_v0_naive.o"
	@echo "... sgemm_v0_naive.i"
	@echo "... sgemm_v0_naive.s"
	@echo "... sgemm_v1.o"
	@echo "... sgemm_v1.i"
	@echo "... sgemm_v1.s"
	@echo "... sgemm_v2.o"
	@echo "... sgemm_v2.i"
	@echo "... sgemm_v2.s"
	@echo "... sgemm_v3.o"
	@echo "... sgemm_v3.i"
	@echo "... sgemm_v3.s"
	@echo "... sgemm_v4.o"
	@echo "... sgemm_v4.i"
	@echo "... sgemm_v4.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

