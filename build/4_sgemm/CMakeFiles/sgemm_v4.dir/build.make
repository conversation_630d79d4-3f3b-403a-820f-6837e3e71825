# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

# Include any dependencies generated for this target.
include 4_sgemm/CMakeFiles/sgemm_v4.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include 4_sgemm/CMakeFiles/sgemm_v4.dir/compiler_depend.make

# Include the progress variables for this target.
include 4_sgemm/CMakeFiles/sgemm_v4.dir/progress.make

# Include the compile flags for this target's objects.
include 4_sgemm/CMakeFiles/sgemm_v4.dir/flags.make

4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o: 4_sgemm/CMakeFiles/sgemm_v4.dir/flags.make
4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o: ../4_sgemm/sgemm_v4.cu
4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o: 4_sgemm/CMakeFiles/sgemm_v4.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CUDA object 4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT 4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o -MF CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o.d -x cu -dc /mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v4.cu -o CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o

4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CUDA source to CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CUDA source to assembly CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

# Object files for target sgemm_v4
sgemm_v4_OBJECTS = \
"CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o"

# External object files for target sgemm_v4
sgemm_v4_EXTERNAL_OBJECTS =

4_sgemm/CMakeFiles/sgemm_v4.dir/cmake_device_link.o: 4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o
4_sgemm/CMakeFiles/sgemm_v4.dir/cmake_device_link.o: 4_sgemm/CMakeFiles/sgemm_v4.dir/build.make
4_sgemm/CMakeFiles/sgemm_v4.dir/cmake_device_link.o: 4_sgemm/libgemm_common.a
4_sgemm/CMakeFiles/sgemm_v4.dir/cmake_device_link.o: /usr/local/cuda/lib64/libcudart.so
4_sgemm/CMakeFiles/sgemm_v4.dir/cmake_device_link.o: /usr/local/cuda/lib64/libcublas.so
4_sgemm/CMakeFiles/sgemm_v4.dir/cmake_device_link.o: 4_sgemm/CMakeFiles/sgemm_v4.dir/dlink.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CUDA device code CMakeFiles/sgemm_v4.dir/cmake_device_link.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sgemm_v4.dir/dlink.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
4_sgemm/CMakeFiles/sgemm_v4.dir/build: 4_sgemm/CMakeFiles/sgemm_v4.dir/cmake_device_link.o
.PHONY : 4_sgemm/CMakeFiles/sgemm_v4.dir/build

# Object files for target sgemm_v4
sgemm_v4_OBJECTS = \
"CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o"

# External object files for target sgemm_v4
sgemm_v4_EXTERNAL_OBJECTS =

4_sgemm/sgemm_v4: 4_sgemm/CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o
4_sgemm/sgemm_v4: 4_sgemm/CMakeFiles/sgemm_v4.dir/build.make
4_sgemm/sgemm_v4: 4_sgemm/libgemm_common.a
4_sgemm/sgemm_v4: /usr/local/cuda/lib64/libcudart.so
4_sgemm/sgemm_v4: /usr/local/cuda/lib64/libcublas.so
4_sgemm/sgemm_v4: 4_sgemm/CMakeFiles/sgemm_v4.dir/cmake_device_link.o
4_sgemm/sgemm_v4: 4_sgemm/CMakeFiles/sgemm_v4.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable sgemm_v4"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sgemm_v4.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
4_sgemm/CMakeFiles/sgemm_v4.dir/build: 4_sgemm/sgemm_v4
.PHONY : 4_sgemm/CMakeFiles/sgemm_v4.dir/build

4_sgemm/CMakeFiles/sgemm_v4.dir/clean:
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && $(CMAKE_COMMAND) -P CMakeFiles/sgemm_v4.dir/cmake_clean.cmake
.PHONY : 4_sgemm/CMakeFiles/sgemm_v4.dir/clean

4_sgemm/CMakeFiles/sgemm_v4.dir/depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/g/project/CUDA/cuda_practice /mnt/g/project/CUDA/cuda_practice/4_sgemm /mnt/g/project/CUDA/cuda_practice/build /mnt/g/project/CUDA/cuda_practice/build/4_sgemm /mnt/g/project/CUDA/cuda_practice/build/4_sgemm/CMakeFiles/sgemm_v4.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : 4_sgemm/CMakeFiles/sgemm_v4.dir/depend

