
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v0_naive.cu" "4_sgemm/CMakeFiles/sgemm_v0_naive.dir/sgemm_v0_naive.cu.o" "gcc" "4_sgemm/CMakeFiles/sgemm_v0_naive.dir/sgemm_v0_naive.cu.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm/CMakeFiles/gemm_common.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
