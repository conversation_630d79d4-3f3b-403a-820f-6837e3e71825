
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/REF_MMult.cpp" "4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o" "gcc" "4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o.d"
  "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/compare_matrices.cpp" "4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o" "gcc" "4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o.d"
  "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/copy_matrix.cpp" "4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o" "gcc" "4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o.d"
  "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/cpu_sgemm.cpp" "4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o" "gcc" "4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o.d"
  "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/dclock.cpp" "4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.o" "gcc" "4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.o.d"
  "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/random_matrix.cpp" "4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o" "gcc" "4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
