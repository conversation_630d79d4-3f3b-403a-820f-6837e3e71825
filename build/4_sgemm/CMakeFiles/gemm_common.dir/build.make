# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

# Include any dependencies generated for this target.
include 4_sgemm/CMakeFiles/gemm_common.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include 4_sgemm/CMakeFiles/gemm_common.dir/compiler_depend.make

# Include the progress variables for this target.
include 4_sgemm/CMakeFiles/gemm_common.dir/progress.make

# Include the compile flags for this target's objects.
include 4_sgemm/CMakeFiles/gemm_common.dir/flags.make

4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/flags.make
4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o: ../4_sgemm/common/REF_MMult.cpp
4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object 4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o -MF CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o.d -o CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/REF_MMult.cpp

4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.i"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/REF_MMult.cpp > CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.i

4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.s"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/REF_MMult.cpp -o CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.s

4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/flags.make
4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o: ../4_sgemm/common/copy_matrix.cpp
4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object 4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o -MF CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o.d -o CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/copy_matrix.cpp

4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.i"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/copy_matrix.cpp > CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.i

4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.s"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/copy_matrix.cpp -o CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.s

4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/flags.make
4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o: ../4_sgemm/common/compare_matrices.cpp
4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object 4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o -MF CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o.d -o CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/compare_matrices.cpp

4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.i"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/compare_matrices.cpp > CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.i

4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.s"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/compare_matrices.cpp -o CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.s

4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/flags.make
4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o: ../4_sgemm/common/random_matrix.cpp
4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object 4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o -MF CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o.d -o CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/random_matrix.cpp

4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/gemm_common.dir/common/random_matrix.cpp.i"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/random_matrix.cpp > CMakeFiles/gemm_common.dir/common/random_matrix.cpp.i

4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/gemm_common.dir/common/random_matrix.cpp.s"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/random_matrix.cpp -o CMakeFiles/gemm_common.dir/common/random_matrix.cpp.s

4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/flags.make
4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.o: ../4_sgemm/common/dclock.cpp
4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object 4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.o -MF CMakeFiles/gemm_common.dir/common/dclock.cpp.o.d -o CMakeFiles/gemm_common.dir/common/dclock.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/dclock.cpp

4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/gemm_common.dir/common/dclock.cpp.i"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/dclock.cpp > CMakeFiles/gemm_common.dir/common/dclock.cpp.i

4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/gemm_common.dir/common/dclock.cpp.s"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/dclock.cpp -o CMakeFiles/gemm_common.dir/common/dclock.cpp.s

4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/flags.make
4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o: ../4_sgemm/common/cpu_sgemm.cpp
4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o: 4_sgemm/CMakeFiles/gemm_common.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object 4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT 4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o -MF CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o.d -o CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/cpu_sgemm.cpp

4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.i"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/cpu_sgemm.cpp > CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.i

4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.s"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/cpu_sgemm.cpp -o CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.s

# Object files for target gemm_common
gemm_common_OBJECTS = \
"CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o" \
"CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o" \
"CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o" \
"CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o" \
"CMakeFiles/gemm_common.dir/common/dclock.cpp.o" \
"CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o"

# External object files for target gemm_common
gemm_common_EXTERNAL_OBJECTS =

4_sgemm/libgemm_common.a: 4_sgemm/CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o
4_sgemm/libgemm_common.a: 4_sgemm/CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o
4_sgemm/libgemm_common.a: 4_sgemm/CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o
4_sgemm/libgemm_common.a: 4_sgemm/CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o
4_sgemm/libgemm_common.a: 4_sgemm/CMakeFiles/gemm_common.dir/common/dclock.cpp.o
4_sgemm/libgemm_common.a: 4_sgemm/CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o
4_sgemm/libgemm_common.a: 4_sgemm/CMakeFiles/gemm_common.dir/build.make
4_sgemm/libgemm_common.a: 4_sgemm/CMakeFiles/gemm_common.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX static library libgemm_common.a"
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && $(CMAKE_COMMAND) -P CMakeFiles/gemm_common.dir/cmake_clean_target.cmake
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/gemm_common.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
4_sgemm/CMakeFiles/gemm_common.dir/build: 4_sgemm/libgemm_common.a
.PHONY : 4_sgemm/CMakeFiles/gemm_common.dir/build

4_sgemm/CMakeFiles/gemm_common.dir/clean:
	cd /mnt/g/project/CUDA/cuda_practice/build/4_sgemm && $(CMAKE_COMMAND) -P CMakeFiles/gemm_common.dir/cmake_clean.cmake
.PHONY : 4_sgemm/CMakeFiles/gemm_common.dir/clean

4_sgemm/CMakeFiles/gemm_common.dir/depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/g/project/CUDA/cuda_practice /mnt/g/project/CUDA/cuda_practice/4_sgemm /mnt/g/project/CUDA/cuda_practice/build /mnt/g/project/CUDA/cuda_practice/build/4_sgemm /mnt/g/project/CUDA/cuda_practice/build/4_sgemm/CMakeFiles/gemm_common.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : 4_sgemm/CMakeFiles/gemm_common.dir/depend

