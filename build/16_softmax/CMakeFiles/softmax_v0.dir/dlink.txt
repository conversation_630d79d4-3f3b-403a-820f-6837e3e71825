/usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler -g --generate-code=arch=compute_75,code=[compute_75,sm_75] --generate-code=arch=compute_80,code=[compute_80,sm_80] --generate-code=arch=compute_86,code=[compute_86,sm_86] --generate-code=arch=compute_89,code=[compute_89,sm_89] -Xcompiler=-fPIC -Wno-deprecated-gpu-targets -shared -dlink CMakeFiles/softmax_v0.dir/softmax_v0.cu.o -o CMakeFiles/softmax_v0.dir/cmake_device_link.o   -lcudadevrt -lcudart_static -lrt -lpthread -ldl 
