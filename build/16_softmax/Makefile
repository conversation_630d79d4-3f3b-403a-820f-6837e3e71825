# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles /mnt/g/project/CUDA/cuda_practice/build/16_softmax//CMakeFiles/progress.marks
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 16_softmax/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 16_softmax/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 16_softmax/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 16_softmax/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
16_softmax/CMakeFiles/softmax_v0.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 16_softmax/CMakeFiles/softmax_v0.dir/rule
.PHONY : 16_softmax/CMakeFiles/softmax_v0.dir/rule

# Convenience name for target.
softmax_v0: 16_softmax/CMakeFiles/softmax_v0.dir/rule
.PHONY : softmax_v0

# fast build rule for target.
softmax_v0/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v0.dir/build.make 16_softmax/CMakeFiles/softmax_v0.dir/build
.PHONY : softmax_v0/fast

# Convenience name for target.
16_softmax/CMakeFiles/softmax_v1.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 16_softmax/CMakeFiles/softmax_v1.dir/rule
.PHONY : 16_softmax/CMakeFiles/softmax_v1.dir/rule

# Convenience name for target.
softmax_v1: 16_softmax/CMakeFiles/softmax_v1.dir/rule
.PHONY : softmax_v1

# fast build rule for target.
softmax_v1/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v1.dir/build.make 16_softmax/CMakeFiles/softmax_v1.dir/build
.PHONY : softmax_v1/fast

# Convenience name for target.
16_softmax/CMakeFiles/softmax_v2.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 16_softmax/CMakeFiles/softmax_v2.dir/rule
.PHONY : 16_softmax/CMakeFiles/softmax_v2.dir/rule

# Convenience name for target.
softmax_v2: 16_softmax/CMakeFiles/softmax_v2.dir/rule
.PHONY : softmax_v2

# fast build rule for target.
softmax_v2/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v2.dir/build.make 16_softmax/CMakeFiles/softmax_v2.dir/build
.PHONY : softmax_v2/fast

# Convenience name for target.
16_softmax/CMakeFiles/softmax_all.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 16_softmax/CMakeFiles/softmax_all.dir/rule
.PHONY : 16_softmax/CMakeFiles/softmax_all.dir/rule

# Convenience name for target.
softmax_all: 16_softmax/CMakeFiles/softmax_all.dir/rule
.PHONY : softmax_all

# fast build rule for target.
softmax_all/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_all.dir/build.make 16_softmax/CMakeFiles/softmax_all.dir/build
.PHONY : softmax_all/fast

softmax_v0.o: softmax_v0.cu.o
.PHONY : softmax_v0.o

# target to build an object file
softmax_v0.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v0.dir/build.make 16_softmax/CMakeFiles/softmax_v0.dir/softmax_v0.cu.o
.PHONY : softmax_v0.cu.o

softmax_v0.i: softmax_v0.cu.i
.PHONY : softmax_v0.i

# target to preprocess a source file
softmax_v0.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v0.dir/build.make 16_softmax/CMakeFiles/softmax_v0.dir/softmax_v0.cu.i
.PHONY : softmax_v0.cu.i

softmax_v0.s: softmax_v0.cu.s
.PHONY : softmax_v0.s

# target to generate assembly for a file
softmax_v0.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v0.dir/build.make 16_softmax/CMakeFiles/softmax_v0.dir/softmax_v0.cu.s
.PHONY : softmax_v0.cu.s

softmax_v1.o: softmax_v1.cu.o
.PHONY : softmax_v1.o

# target to build an object file
softmax_v1.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v1.dir/build.make 16_softmax/CMakeFiles/softmax_v1.dir/softmax_v1.cu.o
.PHONY : softmax_v1.cu.o

softmax_v1.i: softmax_v1.cu.i
.PHONY : softmax_v1.i

# target to preprocess a source file
softmax_v1.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v1.dir/build.make 16_softmax/CMakeFiles/softmax_v1.dir/softmax_v1.cu.i
.PHONY : softmax_v1.cu.i

softmax_v1.s: softmax_v1.cu.s
.PHONY : softmax_v1.s

# target to generate assembly for a file
softmax_v1.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v1.dir/build.make 16_softmax/CMakeFiles/softmax_v1.dir/softmax_v1.cu.s
.PHONY : softmax_v1.cu.s

softmax_v2.o: softmax_v2.cu.o
.PHONY : softmax_v2.o

# target to build an object file
softmax_v2.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v2.dir/build.make 16_softmax/CMakeFiles/softmax_v2.dir/softmax_v2.cu.o
.PHONY : softmax_v2.cu.o

softmax_v2.i: softmax_v2.cu.i
.PHONY : softmax_v2.i

# target to preprocess a source file
softmax_v2.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v2.dir/build.make 16_softmax/CMakeFiles/softmax_v2.dir/softmax_v2.cu.i
.PHONY : softmax_v2.cu.i

softmax_v2.s: softmax_v2.cu.s
.PHONY : softmax_v2.s

# target to generate assembly for a file
softmax_v2.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 16_softmax/CMakeFiles/softmax_v2.dir/build.make 16_softmax/CMakeFiles/softmax_v2.dir/softmax_v2.cu.s
.PHONY : softmax_v2.cu.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... softmax_all"
	@echo "... softmax_v0"
	@echo "... softmax_v1"
	@echo "... softmax_v2"
	@echo "... softmax_v0.o"
	@echo "... softmax_v0.i"
	@echo "... softmax_v0.s"
	@echo "... softmax_v1.o"
	@echo "... softmax_v1.i"
	@echo "... softmax_v1.s"
	@echo "... softmax_v2.o"
	@echo "... softmax_v2.i"
	@echo "... softmax_v2.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

