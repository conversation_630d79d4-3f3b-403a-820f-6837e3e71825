# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

# Utility rule file for dot_product_all.

# Include any custom commands dependencies for this target.
include 15_dot-product/CMakeFiles/dot_product_all.dir/compiler_depend.make

# Include the progress variables for this target.
include 15_dot-product/CMakeFiles/dot_product_all.dir/progress.make

15_dot-product/CMakeFiles/dot_product_all: 15_dot-product/dot_product_v0
15_dot-product/CMakeFiles/dot_product_all: 15_dot-product/dot_product_v1
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building all dot product versions"

dot_product_all: 15_dot-product/CMakeFiles/dot_product_all
dot_product_all: 15_dot-product/CMakeFiles/dot_product_all.dir/build.make
.PHONY : dot_product_all

# Rule to build all files generated by this target.
15_dot-product/CMakeFiles/dot_product_all.dir/build: dot_product_all
.PHONY : 15_dot-product/CMakeFiles/dot_product_all.dir/build

15_dot-product/CMakeFiles/dot_product_all.dir/clean:
	cd /mnt/g/project/CUDA/cuda_practice/build/15_dot-product && $(CMAKE_COMMAND) -P CMakeFiles/dot_product_all.dir/cmake_clean.cmake
.PHONY : 15_dot-product/CMakeFiles/dot_product_all.dir/clean

15_dot-product/CMakeFiles/dot_product_all.dir/depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/g/project/CUDA/cuda_practice /mnt/g/project/CUDA/cuda_practice/15_dot-product /mnt/g/project/CUDA/cuda_practice/build /mnt/g/project/CUDA/cuda_practice/build/15_dot-product /mnt/g/project/CUDA/cuda_practice/build/15_dot-product/CMakeFiles/dot_product_all.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : 15_dot-product/CMakeFiles/dot_product_all.dir/depend

