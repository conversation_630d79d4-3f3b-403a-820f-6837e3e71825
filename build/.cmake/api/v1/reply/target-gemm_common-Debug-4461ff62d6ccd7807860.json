{"archive": {}, "artifacts": [{"path": "4_sgemm/libgemm_common.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_include_directories"], "files": ["4_sgemm/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 2, "parent": 0}, {"command": 1, "file": 0, "line": 12, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}], "includes": [{"backtrace": 2, "path": "/mnt/g/project/CUDA/cuda_practice/4_sgemm"}, {"backtrace": 2, "path": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "id": "gemm_common::@00f1f9e5938d9f3d3522", "name": "gemm_common", "nameOnDisk": "libgemm_common.a", "paths": {"build": "4_sgemm", "source": "4_sgemm"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "4_sgemm/common/REF_MMult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "4_sgemm/common/copy_matrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "4_sgemm/common/compare_matrices.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "4_sgemm/common/random_matrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "4_sgemm/common/dclock.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "4_sgemm/common/cpu_sgemm.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}