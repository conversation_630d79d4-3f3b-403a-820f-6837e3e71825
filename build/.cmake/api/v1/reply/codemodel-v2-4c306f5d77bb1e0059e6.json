{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.20.0"}, "projectIndex": 0, "source": "."}, {"build": "4_sgemm", "jsonFile": "directory-4_sgemm-Debug-c98aba2cb7a578e7aa8c.json", "minimumCMakeVersion": {"string": "3.20.0"}, "parentIndex": 0, "projectIndex": 0, "source": "4_sgemm", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "cuda_practice", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 1, "id": "gemm_common::@00f1f9e5938d9f3d3522", "jsonFile": "target-gemm_common-Debug-4461ff62d6ccd7807860.json", "name": "gemm_common", "projectIndex": 0}, {"directoryIndex": 1, "id": "sgemm_v0_naive::@00f1f9e5938d9f3d3522", "jsonFile": "target-sgemm_v0_naive-Debug-920f044604b68a162c39.json", "name": "sgemm_v0_naive", "projectIndex": 0}, {"directoryIndex": 1, "id": "sgemm_v1::@00f1f9e5938d9f3d3522", "jsonFile": "target-sgemm_v1-Debug-11036315debdcdf56a64.json", "name": "sgemm_v1", "projectIndex": 0}, {"directoryIndex": 1, "id": "sgemm_v2::@00f1f9e5938d9f3d3522", "jsonFile": "target-sgemm_v2-Debug-f04ab263398b0c07246f.json", "name": "sgemm_v2", "projectIndex": 0}, {"directoryIndex": 1, "id": "sgemm_v3::@00f1f9e5938d9f3d3522", "jsonFile": "target-sgemm_v3-Debug-095f859f4bf23d3b84fc.json", "name": "sgemm_v3", "projectIndex": 0}, {"directoryIndex": 1, "id": "sgemm_v4::@00f1f9e5938d9f3d3522", "jsonFile": "target-sgemm_v4-Debug-8c899ea2f0aaf4a06841.json", "name": "sgemm_v4", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/mnt/g/project/CUDA/cuda_practice/build", "source": "/mnt/g/project/CUDA/cuda_practice"}, "version": {"major": 2, "minor": 3}}