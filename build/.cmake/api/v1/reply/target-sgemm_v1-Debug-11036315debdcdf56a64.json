{"artifacts": [{"path": "4_sgemm/sgemm_v1"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["4_sgemm/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 31, "parent": 0}, {"command": 1, "file": 0, "line": 32, "parent": 0}, {"command": 2, "file": 0, "line": 39, "parent": 0}, {"command": 3, "file": 0, "line": 34, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g --generate-code=arch=compute_89,code=[compute_89,sm_89]"}, {"backtrace": 3, "fragment": "-G"}], "includes": [{"backtrace": 4, "path": "/mnt/g/project/CUDA/cuda_practice/4_sgemm"}, {"backtrace": 4, "path": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common"}, {"backtrace": 2, "isSystem": true, "path": "/usr/local/cuda/include"}], "language": "CUDA", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "gemm_common::@00f1f9e5938d9f3d3522"}], "id": "sgemm_v1::@00f1f9e5938d9f3d3522", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-L/usr/local/cuda/targets/x86_64-linux/lib/stubs", "role": "libraryPath"}, {"fragment": "-L/usr/local/cuda/targets/x86_64-linux/lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/usr/local/cuda/lib64", "role": "libraries"}, {"backtrace": 2, "fragment": "libgemm_common.a", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/cuda/lib64/libcudart.so", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/cuda/lib64/libcublas.so", "role": "libraries"}, {"fragment": "-lcu<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-lcudart_static", "role": "libraries"}, {"fragment": "-lrt", "role": "libraries"}, {"fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "sgemm_v1", "nameOnDisk": "sgemm_v1", "paths": {"build": "4_sgemm", "source": "4_sgemm"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "4_sgemm/sgemm_v1.cu", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}