# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: 4_sgemm/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: 4_sgemm/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: 4_sgemm/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory 4_sgemm

# Recursive "all" directory target.
4_sgemm/all: 4_sgemm/CMakeFiles/gemm_common.dir/all
4_sgemm/all: 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/all
4_sgemm/all: 4_sgemm/CMakeFiles/sgemm_v1.dir/all
4_sgemm/all: 4_sgemm/CMakeFiles/sgemm_v2.dir/all
4_sgemm/all: 4_sgemm/CMakeFiles/sgemm_v3.dir/all
4_sgemm/all: 4_sgemm/CMakeFiles/sgemm_v4.dir/all
.PHONY : 4_sgemm/all

# Recursive "preinstall" directory target.
4_sgemm/preinstall:
.PHONY : 4_sgemm/preinstall

# Recursive "clean" directory target.
4_sgemm/clean: 4_sgemm/CMakeFiles/gemm_common.dir/clean
4_sgemm/clean: 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/clean
4_sgemm/clean: 4_sgemm/CMakeFiles/sgemm_v1.dir/clean
4_sgemm/clean: 4_sgemm/CMakeFiles/sgemm_v2.dir/clean
4_sgemm/clean: 4_sgemm/CMakeFiles/sgemm_v3.dir/clean
4_sgemm/clean: 4_sgemm/CMakeFiles/sgemm_v4.dir/clean
.PHONY : 4_sgemm/clean

#=============================================================================
# Target rules for target 4_sgemm/CMakeFiles/gemm_common.dir

# All Build rule for target.
4_sgemm/CMakeFiles/gemm_common.dir/all:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/depend
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=1,2,3,4,5,6,7 "Built target gemm_common"
.PHONY : 4_sgemm/CMakeFiles/gemm_common.dir/all

# Build rule for subdir invocation for target.
4_sgemm/CMakeFiles/gemm_common.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/gemm_common.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : 4_sgemm/CMakeFiles/gemm_common.dir/rule

# Convenience name for target.
gemm_common: 4_sgemm/CMakeFiles/gemm_common.dir/rule
.PHONY : gemm_common

# clean rule for target.
4_sgemm/CMakeFiles/gemm_common.dir/clean:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/gemm_common.dir/build.make 4_sgemm/CMakeFiles/gemm_common.dir/clean
.PHONY : 4_sgemm/CMakeFiles/gemm_common.dir/clean

#=============================================================================
# Target rules for target 4_sgemm/CMakeFiles/sgemm_v0_naive.dir

# All Build rule for target.
4_sgemm/CMakeFiles/sgemm_v0_naive.dir/all: 4_sgemm/CMakeFiles/gemm_common.dir/all
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build.make 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/depend
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build.make 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=8,9,10 "Built target sgemm_v0_naive"
.PHONY : 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/all

# Build rule for subdir invocation for target.
4_sgemm/CMakeFiles/sgemm_v0_naive.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/rule

# Convenience name for target.
sgemm_v0_naive: 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/rule
.PHONY : sgemm_v0_naive

# clean rule for target.
4_sgemm/CMakeFiles/sgemm_v0_naive.dir/clean:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/build.make 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/clean
.PHONY : 4_sgemm/CMakeFiles/sgemm_v0_naive.dir/clean

#=============================================================================
# Target rules for target 4_sgemm/CMakeFiles/sgemm_v1.dir

# All Build rule for target.
4_sgemm/CMakeFiles/sgemm_v1.dir/all: 4_sgemm/CMakeFiles/gemm_common.dir/all
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v1.dir/build.make 4_sgemm/CMakeFiles/sgemm_v1.dir/depend
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v1.dir/build.make 4_sgemm/CMakeFiles/sgemm_v1.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=11,12,13 "Built target sgemm_v1"
.PHONY : 4_sgemm/CMakeFiles/sgemm_v1.dir/all

# Build rule for subdir invocation for target.
4_sgemm/CMakeFiles/sgemm_v1.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/sgemm_v1.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : 4_sgemm/CMakeFiles/sgemm_v1.dir/rule

# Convenience name for target.
sgemm_v1: 4_sgemm/CMakeFiles/sgemm_v1.dir/rule
.PHONY : sgemm_v1

# clean rule for target.
4_sgemm/CMakeFiles/sgemm_v1.dir/clean:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v1.dir/build.make 4_sgemm/CMakeFiles/sgemm_v1.dir/clean
.PHONY : 4_sgemm/CMakeFiles/sgemm_v1.dir/clean

#=============================================================================
# Target rules for target 4_sgemm/CMakeFiles/sgemm_v2.dir

# All Build rule for target.
4_sgemm/CMakeFiles/sgemm_v2.dir/all: 4_sgemm/CMakeFiles/gemm_common.dir/all
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v2.dir/build.make 4_sgemm/CMakeFiles/sgemm_v2.dir/depend
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v2.dir/build.make 4_sgemm/CMakeFiles/sgemm_v2.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=14,15,16 "Built target sgemm_v2"
.PHONY : 4_sgemm/CMakeFiles/sgemm_v2.dir/all

# Build rule for subdir invocation for target.
4_sgemm/CMakeFiles/sgemm_v2.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/sgemm_v2.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : 4_sgemm/CMakeFiles/sgemm_v2.dir/rule

# Convenience name for target.
sgemm_v2: 4_sgemm/CMakeFiles/sgemm_v2.dir/rule
.PHONY : sgemm_v2

# clean rule for target.
4_sgemm/CMakeFiles/sgemm_v2.dir/clean:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v2.dir/build.make 4_sgemm/CMakeFiles/sgemm_v2.dir/clean
.PHONY : 4_sgemm/CMakeFiles/sgemm_v2.dir/clean

#=============================================================================
# Target rules for target 4_sgemm/CMakeFiles/sgemm_v3.dir

# All Build rule for target.
4_sgemm/CMakeFiles/sgemm_v3.dir/all: 4_sgemm/CMakeFiles/gemm_common.dir/all
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v3.dir/build.make 4_sgemm/CMakeFiles/sgemm_v3.dir/depend
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v3.dir/build.make 4_sgemm/CMakeFiles/sgemm_v3.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=17,18,19 "Built target sgemm_v3"
.PHONY : 4_sgemm/CMakeFiles/sgemm_v3.dir/all

# Build rule for subdir invocation for target.
4_sgemm/CMakeFiles/sgemm_v3.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/sgemm_v3.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : 4_sgemm/CMakeFiles/sgemm_v3.dir/rule

# Convenience name for target.
sgemm_v3: 4_sgemm/CMakeFiles/sgemm_v3.dir/rule
.PHONY : sgemm_v3

# clean rule for target.
4_sgemm/CMakeFiles/sgemm_v3.dir/clean:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v3.dir/build.make 4_sgemm/CMakeFiles/sgemm_v3.dir/clean
.PHONY : 4_sgemm/CMakeFiles/sgemm_v3.dir/clean

#=============================================================================
# Target rules for target 4_sgemm/CMakeFiles/sgemm_v4.dir

# All Build rule for target.
4_sgemm/CMakeFiles/sgemm_v4.dir/all: 4_sgemm/CMakeFiles/gemm_common.dir/all
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v4.dir/build.make 4_sgemm/CMakeFiles/sgemm_v4.dir/depend
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v4.dir/build.make 4_sgemm/CMakeFiles/sgemm_v4.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=20,21,22 "Built target sgemm_v4"
.PHONY : 4_sgemm/CMakeFiles/sgemm_v4.dir/all

# Build rule for subdir invocation for target.
4_sgemm/CMakeFiles/sgemm_v4.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 4_sgemm/CMakeFiles/sgemm_v4.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : 4_sgemm/CMakeFiles/sgemm_v4.dir/rule

# Convenience name for target.
sgemm_v4: 4_sgemm/CMakeFiles/sgemm_v4.dir/rule
.PHONY : sgemm_v4

# clean rule for target.
4_sgemm/CMakeFiles/sgemm_v4.dir/clean:
	$(MAKE) $(MAKESILENT) -f 4_sgemm/CMakeFiles/sgemm_v4.dir/build.make 4_sgemm/CMakeFiles/sgemm_v4.dir/clean
.PHONY : 4_sgemm/CMakeFiles/sgemm_v4.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

