# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

# Utility rule file for create_matrix_dir.

# Include any custom commands dependencies for this target.
include 13_spmm/CMakeFiles/create_matrix_dir.dir/compiler_depend.make

# Include the progress variables for this target.
include 13_spmm/CMakeFiles/create_matrix_dir.dir/progress.make

13_spmm/CMakeFiles/create_matrix_dir:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Creating matrix directory for test files"
	cd /mnt/g/project/CUDA/cuda_practice/build/13_spmm && /usr/bin/cmake -E make_directory /mnt/g/project/CUDA/cuda_practice/build/13_spmm/matrix

create_matrix_dir: 13_spmm/CMakeFiles/create_matrix_dir
create_matrix_dir: 13_spmm/CMakeFiles/create_matrix_dir.dir/build.make
.PHONY : create_matrix_dir

# Rule to build all files generated by this target.
13_spmm/CMakeFiles/create_matrix_dir.dir/build: create_matrix_dir
.PHONY : 13_spmm/CMakeFiles/create_matrix_dir.dir/build

13_spmm/CMakeFiles/create_matrix_dir.dir/clean:
	cd /mnt/g/project/CUDA/cuda_practice/build/13_spmm && $(CMAKE_COMMAND) -P CMakeFiles/create_matrix_dir.dir/cmake_clean.cmake
.PHONY : 13_spmm/CMakeFiles/create_matrix_dir.dir/clean

13_spmm/CMakeFiles/create_matrix_dir.dir/depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/g/project/CUDA/cuda_practice /mnt/g/project/CUDA/cuda_practice/13_spmm /mnt/g/project/CUDA/cuda_practice/build /mnt/g/project/CUDA/cuda_practice/build/13_spmm /mnt/g/project/CUDA/cuda_practice/build/13_spmm/CMakeFiles/create_matrix_dir.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : 13_spmm/CMakeFiles/create_matrix_dir.dir/depend

