# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

# Utility rule file for copy_test_matrices.

# Include any custom commands dependencies for this target.
include 13_spmm/CMakeFiles/copy_test_matrices.dir/compiler_depend.make

# Include the progress variables for this target.
include 13_spmm/CMakeFiles/copy_test_matrices.dir/progress.make

13_spmm/CMakeFiles/copy_test_matrices:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Copying test matrices to build directory"
	cd /mnt/g/project/CUDA/cuda_practice/build/13_spmm && /usr/bin/cmake -E copy_directory /mnt/g/project/CUDA/cuda_practice/13_spmm/matrix /mnt/g/project/CUDA/cuda_practice/build/13_spmm/matrix

copy_test_matrices: 13_spmm/CMakeFiles/copy_test_matrices
copy_test_matrices: 13_spmm/CMakeFiles/copy_test_matrices.dir/build.make
.PHONY : copy_test_matrices

# Rule to build all files generated by this target.
13_spmm/CMakeFiles/copy_test_matrices.dir/build: copy_test_matrices
.PHONY : 13_spmm/CMakeFiles/copy_test_matrices.dir/build

13_spmm/CMakeFiles/copy_test_matrices.dir/clean:
	cd /mnt/g/project/CUDA/cuda_practice/build/13_spmm && $(CMAKE_COMMAND) -P CMakeFiles/copy_test_matrices.dir/cmake_clean.cmake
.PHONY : 13_spmm/CMakeFiles/copy_test_matrices.dir/clean

13_spmm/CMakeFiles/copy_test_matrices.dir/depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/g/project/CUDA/cuda_practice /mnt/g/project/CUDA/cuda_practice/13_spmm /mnt/g/project/CUDA/cuda_practice/build /mnt/g/project/CUDA/cuda_practice/build/13_spmm /mnt/g/project/CUDA/cuda_practice/build/13_spmm/CMakeFiles/copy_test_matrices.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : 13_spmm/CMakeFiles/copy_test_matrices.dir/depend

