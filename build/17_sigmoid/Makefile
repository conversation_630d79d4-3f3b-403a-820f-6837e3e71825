# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles /mnt/g/project/CUDA/cuda_practice/build/17_sigmoid//CMakeFiles/progress.marks
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 17_sigmoid/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 17_sigmoid/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 17_sigmoid/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 17_sigmoid/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
17_sigmoid/CMakeFiles/sigmoid.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 17_sigmoid/CMakeFiles/sigmoid.dir/rule
.PHONY : 17_sigmoid/CMakeFiles/sigmoid.dir/rule

# Convenience name for target.
sigmoid: 17_sigmoid/CMakeFiles/sigmoid.dir/rule
.PHONY : sigmoid

# fast build rule for target.
sigmoid/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid.dir/build.make 17_sigmoid/CMakeFiles/sigmoid.dir/build
.PHONY : sigmoid/fast

# Convenience name for target.
17_sigmoid/CMakeFiles/sigmoid_v0.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 17_sigmoid/CMakeFiles/sigmoid_v0.dir/rule
.PHONY : 17_sigmoid/CMakeFiles/sigmoid_v0.dir/rule

# Convenience name for target.
sigmoid_v0: 17_sigmoid/CMakeFiles/sigmoid_v0.dir/rule
.PHONY : sigmoid_v0

# fast build rule for target.
sigmoid_v0/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid_v0.dir/build.make 17_sigmoid/CMakeFiles/sigmoid_v0.dir/build
.PHONY : sigmoid_v0/fast

# Convenience name for target.
17_sigmoid/CMakeFiles/sigmoid_v1.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 17_sigmoid/CMakeFiles/sigmoid_v1.dir/rule
.PHONY : 17_sigmoid/CMakeFiles/sigmoid_v1.dir/rule

# Convenience name for target.
sigmoid_v1: 17_sigmoid/CMakeFiles/sigmoid_v1.dir/rule
.PHONY : sigmoid_v1

# fast build rule for target.
sigmoid_v1/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid_v1.dir/build.make 17_sigmoid/CMakeFiles/sigmoid_v1.dir/build
.PHONY : sigmoid_v1/fast

sigmoid.o: sigmoid.cu.o
.PHONY : sigmoid.o

# target to build an object file
sigmoid.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid.dir/build.make 17_sigmoid/CMakeFiles/sigmoid.dir/sigmoid.cu.o
.PHONY : sigmoid.cu.o

sigmoid.i: sigmoid.cu.i
.PHONY : sigmoid.i

# target to preprocess a source file
sigmoid.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid.dir/build.make 17_sigmoid/CMakeFiles/sigmoid.dir/sigmoid.cu.i
.PHONY : sigmoid.cu.i

sigmoid.s: sigmoid.cu.s
.PHONY : sigmoid.s

# target to generate assembly for a file
sigmoid.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid.dir/build.make 17_sigmoid/CMakeFiles/sigmoid.dir/sigmoid.cu.s
.PHONY : sigmoid.cu.s

sigmoid_v0.o: sigmoid_v0.cu.o
.PHONY : sigmoid_v0.o

# target to build an object file
sigmoid_v0.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid_v0.dir/build.make 17_sigmoid/CMakeFiles/sigmoid_v0.dir/sigmoid_v0.cu.o
.PHONY : sigmoid_v0.cu.o

sigmoid_v0.i: sigmoid_v0.cu.i
.PHONY : sigmoid_v0.i

# target to preprocess a source file
sigmoid_v0.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid_v0.dir/build.make 17_sigmoid/CMakeFiles/sigmoid_v0.dir/sigmoid_v0.cu.i
.PHONY : sigmoid_v0.cu.i

sigmoid_v0.s: sigmoid_v0.cu.s
.PHONY : sigmoid_v0.s

# target to generate assembly for a file
sigmoid_v0.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid_v0.dir/build.make 17_sigmoid/CMakeFiles/sigmoid_v0.dir/sigmoid_v0.cu.s
.PHONY : sigmoid_v0.cu.s

sigmoid_v1.o: sigmoid_v1.cu.o
.PHONY : sigmoid_v1.o

# target to build an object file
sigmoid_v1.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid_v1.dir/build.make 17_sigmoid/CMakeFiles/sigmoid_v1.dir/sigmoid_v1.cu.o
.PHONY : sigmoid_v1.cu.o

sigmoid_v1.i: sigmoid_v1.cu.i
.PHONY : sigmoid_v1.i

# target to preprocess a source file
sigmoid_v1.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid_v1.dir/build.make 17_sigmoid/CMakeFiles/sigmoid_v1.dir/sigmoid_v1.cu.i
.PHONY : sigmoid_v1.cu.i

sigmoid_v1.s: sigmoid_v1.cu.s
.PHONY : sigmoid_v1.s

# target to generate assembly for a file
sigmoid_v1.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 17_sigmoid/CMakeFiles/sigmoid_v1.dir/build.make 17_sigmoid/CMakeFiles/sigmoid_v1.dir/sigmoid_v1.cu.s
.PHONY : sigmoid_v1.cu.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... sigmoid"
	@echo "... sigmoid_v0"
	@echo "... sigmoid_v1"
	@echo "... sigmoid.o"
	@echo "... sigmoid.i"
	@echo "... sigmoid.s"
	@echo "... sigmoid_v0.o"
	@echo "... sigmoid_v0.i"
	@echo "... sigmoid_v0.s"
	@echo "... sigmoid_v1.o"
	@echo "... sigmoid_v1.i"
	@echo "... sigmoid_v1.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

