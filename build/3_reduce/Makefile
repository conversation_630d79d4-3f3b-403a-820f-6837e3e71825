# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles /mnt/g/project/CUDA/cuda_practice/build/3_reduce//CMakeFiles/progress.marks
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
3_reduce/CMakeFiles/reduce_v0_baseline.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/reduce_v0_baseline.dir/rule
.PHONY : 3_reduce/CMakeFiles/reduce_v0_baseline.dir/rule

# Convenience name for target.
reduce_v0_baseline: 3_reduce/CMakeFiles/reduce_v0_baseline.dir/rule
.PHONY : reduce_v0_baseline

# fast build rule for target.
reduce_v0_baseline/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v0_baseline.dir/build.make 3_reduce/CMakeFiles/reduce_v0_baseline.dir/build
.PHONY : reduce_v0_baseline/fast

# Convenience name for target.
3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/rule
.PHONY : 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/rule

# Convenience name for target.
reduce_v1_no_divergence_branch: 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/rule
.PHONY : reduce_v1_no_divergence_branch

# fast build rule for target.
reduce_v1_no_divergence_branch/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/build.make 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/build
.PHONY : reduce_v1_no_divergence_branch/fast

# Convenience name for target.
3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/rule
.PHONY : 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/rule

# Convenience name for target.
reduce_v2_no_bank_conflict: 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/rule
.PHONY : reduce_v2_no_bank_conflict

# fast build rule for target.
reduce_v2_no_bank_conflict/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/build.make 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/build
.PHONY : reduce_v2_no_bank_conflict/fast

# Convenience name for target.
3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/rule
.PHONY : 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/rule

# Convenience name for target.
reduce_v3_add_during_load: 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/rule
.PHONY : reduce_v3_add_during_load

# fast build rule for target.
reduce_v3_add_during_load/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/build.make 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/build
.PHONY : reduce_v3_add_during_load/fast

# Convenience name for target.
3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/rule
.PHONY : 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/rule

# Convenience name for target.
reduce_v4_unroll_last_warp: 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/rule
.PHONY : reduce_v4_unroll_last_warp

# fast build rule for target.
reduce_v4_unroll_last_warp/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/build.make 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/build
.PHONY : reduce_v4_unroll_last_warp/fast

# Convenience name for target.
3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/rule
.PHONY : 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/rule

# Convenience name for target.
reduce_v5_completely_unroll: 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/rule
.PHONY : reduce_v5_completely_unroll

# fast build rule for target.
reduce_v5_completely_unroll/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/build.make 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/build
.PHONY : reduce_v5_completely_unroll/fast

# Convenience name for target.
3_reduce/CMakeFiles/reduce_v6_multi_add.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/rule
.PHONY : 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/rule

# Convenience name for target.
reduce_v6_multi_add: 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/rule
.PHONY : reduce_v6_multi_add

# fast build rule for target.
reduce_v6_multi_add/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/build.make 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/build
.PHONY : reduce_v6_multi_add/fast

# Convenience name for target.
3_reduce/CMakeFiles/reduce_v7_shuffle.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/rule
.PHONY : 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/rule

# Convenience name for target.
reduce_v7_shuffle: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/rule
.PHONY : reduce_v7_shuffle

# fast build rule for target.
reduce_v7_shuffle/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build.make 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build
.PHONY : reduce_v7_shuffle/fast

# Convenience name for target.
3_reduce/CMakeFiles/block_all_reduce.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/block_all_reduce.dir/rule
.PHONY : 3_reduce/CMakeFiles/block_all_reduce.dir/rule

# Convenience name for target.
block_all_reduce: 3_reduce/CMakeFiles/block_all_reduce.dir/rule
.PHONY : block_all_reduce

# fast build rule for target.
block_all_reduce/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce.dir/build.make 3_reduce/CMakeFiles/block_all_reduce.dir/build
.PHONY : block_all_reduce/fast

# Convenience name for target.
3_reduce/CMakeFiles/block_all_reduce_v0.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/block_all_reduce_v0.dir/rule
.PHONY : 3_reduce/CMakeFiles/block_all_reduce_v0.dir/rule

# Convenience name for target.
block_all_reduce_v0: 3_reduce/CMakeFiles/block_all_reduce_v0.dir/rule
.PHONY : block_all_reduce_v0

# fast build rule for target.
block_all_reduce_v0/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce_v0.dir/build.make 3_reduce/CMakeFiles/block_all_reduce_v0.dir/build
.PHONY : block_all_reduce_v0/fast

# Convenience name for target.
3_reduce/CMakeFiles/block_all_reduce_v1.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 3_reduce/CMakeFiles/block_all_reduce_v1.dir/rule
.PHONY : 3_reduce/CMakeFiles/block_all_reduce_v1.dir/rule

# Convenience name for target.
block_all_reduce_v1: 3_reduce/CMakeFiles/block_all_reduce_v1.dir/rule
.PHONY : block_all_reduce_v1

# fast build rule for target.
block_all_reduce_v1/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce_v1.dir/build.make 3_reduce/CMakeFiles/block_all_reduce_v1.dir/build
.PHONY : block_all_reduce_v1/fast

block_all_reduce.o: block_all_reduce.cu.o
.PHONY : block_all_reduce.o

# target to build an object file
block_all_reduce.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce.dir/build.make 3_reduce/CMakeFiles/block_all_reduce.dir/block_all_reduce.cu.o
.PHONY : block_all_reduce.cu.o

block_all_reduce.i: block_all_reduce.cu.i
.PHONY : block_all_reduce.i

# target to preprocess a source file
block_all_reduce.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce.dir/build.make 3_reduce/CMakeFiles/block_all_reduce.dir/block_all_reduce.cu.i
.PHONY : block_all_reduce.cu.i

block_all_reduce.s: block_all_reduce.cu.s
.PHONY : block_all_reduce.s

# target to generate assembly for a file
block_all_reduce.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce.dir/build.make 3_reduce/CMakeFiles/block_all_reduce.dir/block_all_reduce.cu.s
.PHONY : block_all_reduce.cu.s

block_all_reduce_v0.o: block_all_reduce_v0.cu.o
.PHONY : block_all_reduce_v0.o

# target to build an object file
block_all_reduce_v0.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce_v0.dir/build.make 3_reduce/CMakeFiles/block_all_reduce_v0.dir/block_all_reduce_v0.cu.o
.PHONY : block_all_reduce_v0.cu.o

block_all_reduce_v0.i: block_all_reduce_v0.cu.i
.PHONY : block_all_reduce_v0.i

# target to preprocess a source file
block_all_reduce_v0.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce_v0.dir/build.make 3_reduce/CMakeFiles/block_all_reduce_v0.dir/block_all_reduce_v0.cu.i
.PHONY : block_all_reduce_v0.cu.i

block_all_reduce_v0.s: block_all_reduce_v0.cu.s
.PHONY : block_all_reduce_v0.s

# target to generate assembly for a file
block_all_reduce_v0.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce_v0.dir/build.make 3_reduce/CMakeFiles/block_all_reduce_v0.dir/block_all_reduce_v0.cu.s
.PHONY : block_all_reduce_v0.cu.s

block_all_reduce_v1.o: block_all_reduce_v1.cu.o
.PHONY : block_all_reduce_v1.o

# target to build an object file
block_all_reduce_v1.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce_v1.dir/build.make 3_reduce/CMakeFiles/block_all_reduce_v1.dir/block_all_reduce_v1.cu.o
.PHONY : block_all_reduce_v1.cu.o

block_all_reduce_v1.i: block_all_reduce_v1.cu.i
.PHONY : block_all_reduce_v1.i

# target to preprocess a source file
block_all_reduce_v1.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce_v1.dir/build.make 3_reduce/CMakeFiles/block_all_reduce_v1.dir/block_all_reduce_v1.cu.i
.PHONY : block_all_reduce_v1.cu.i

block_all_reduce_v1.s: block_all_reduce_v1.cu.s
.PHONY : block_all_reduce_v1.s

# target to generate assembly for a file
block_all_reduce_v1.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/block_all_reduce_v1.dir/build.make 3_reduce/CMakeFiles/block_all_reduce_v1.dir/block_all_reduce_v1.cu.s
.PHONY : block_all_reduce_v1.cu.s

reduce_v0_baseline.o: reduce_v0_baseline.cu.o
.PHONY : reduce_v0_baseline.o

# target to build an object file
reduce_v0_baseline.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v0_baseline.dir/build.make 3_reduce/CMakeFiles/reduce_v0_baseline.dir/reduce_v0_baseline.cu.o
.PHONY : reduce_v0_baseline.cu.o

reduce_v0_baseline.i: reduce_v0_baseline.cu.i
.PHONY : reduce_v0_baseline.i

# target to preprocess a source file
reduce_v0_baseline.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v0_baseline.dir/build.make 3_reduce/CMakeFiles/reduce_v0_baseline.dir/reduce_v0_baseline.cu.i
.PHONY : reduce_v0_baseline.cu.i

reduce_v0_baseline.s: reduce_v0_baseline.cu.s
.PHONY : reduce_v0_baseline.s

# target to generate assembly for a file
reduce_v0_baseline.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v0_baseline.dir/build.make 3_reduce/CMakeFiles/reduce_v0_baseline.dir/reduce_v0_baseline.cu.s
.PHONY : reduce_v0_baseline.cu.s

reduce_v1_no_divergence_branch.o: reduce_v1_no_divergence_branch.cu.o
.PHONY : reduce_v1_no_divergence_branch.o

# target to build an object file
reduce_v1_no_divergence_branch.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/build.make 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/reduce_v1_no_divergence_branch.cu.o
.PHONY : reduce_v1_no_divergence_branch.cu.o

reduce_v1_no_divergence_branch.i: reduce_v1_no_divergence_branch.cu.i
.PHONY : reduce_v1_no_divergence_branch.i

# target to preprocess a source file
reduce_v1_no_divergence_branch.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/build.make 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/reduce_v1_no_divergence_branch.cu.i
.PHONY : reduce_v1_no_divergence_branch.cu.i

reduce_v1_no_divergence_branch.s: reduce_v1_no_divergence_branch.cu.s
.PHONY : reduce_v1_no_divergence_branch.s

# target to generate assembly for a file
reduce_v1_no_divergence_branch.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/build.make 3_reduce/CMakeFiles/reduce_v1_no_divergence_branch.dir/reduce_v1_no_divergence_branch.cu.s
.PHONY : reduce_v1_no_divergence_branch.cu.s

reduce_v2_no_bank_conflict.o: reduce_v2_no_bank_conflict.cu.o
.PHONY : reduce_v2_no_bank_conflict.o

# target to build an object file
reduce_v2_no_bank_conflict.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/build.make 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/reduce_v2_no_bank_conflict.cu.o
.PHONY : reduce_v2_no_bank_conflict.cu.o

reduce_v2_no_bank_conflict.i: reduce_v2_no_bank_conflict.cu.i
.PHONY : reduce_v2_no_bank_conflict.i

# target to preprocess a source file
reduce_v2_no_bank_conflict.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/build.make 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/reduce_v2_no_bank_conflict.cu.i
.PHONY : reduce_v2_no_bank_conflict.cu.i

reduce_v2_no_bank_conflict.s: reduce_v2_no_bank_conflict.cu.s
.PHONY : reduce_v2_no_bank_conflict.s

# target to generate assembly for a file
reduce_v2_no_bank_conflict.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/build.make 3_reduce/CMakeFiles/reduce_v2_no_bank_conflict.dir/reduce_v2_no_bank_conflict.cu.s
.PHONY : reduce_v2_no_bank_conflict.cu.s

reduce_v3_add_during_load.o: reduce_v3_add_during_load.cu.o
.PHONY : reduce_v3_add_during_load.o

# target to build an object file
reduce_v3_add_during_load.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/build.make 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/reduce_v3_add_during_load.cu.o
.PHONY : reduce_v3_add_during_load.cu.o

reduce_v3_add_during_load.i: reduce_v3_add_during_load.cu.i
.PHONY : reduce_v3_add_during_load.i

# target to preprocess a source file
reduce_v3_add_during_load.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/build.make 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/reduce_v3_add_during_load.cu.i
.PHONY : reduce_v3_add_during_load.cu.i

reduce_v3_add_during_load.s: reduce_v3_add_during_load.cu.s
.PHONY : reduce_v3_add_during_load.s

# target to generate assembly for a file
reduce_v3_add_during_load.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/build.make 3_reduce/CMakeFiles/reduce_v3_add_during_load.dir/reduce_v3_add_during_load.cu.s
.PHONY : reduce_v3_add_during_load.cu.s

reduce_v4_unroll_last_warp.o: reduce_v4_unroll_last_warp.cu.o
.PHONY : reduce_v4_unroll_last_warp.o

# target to build an object file
reduce_v4_unroll_last_warp.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/build.make 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/reduce_v4_unroll_last_warp.cu.o
.PHONY : reduce_v4_unroll_last_warp.cu.o

reduce_v4_unroll_last_warp.i: reduce_v4_unroll_last_warp.cu.i
.PHONY : reduce_v4_unroll_last_warp.i

# target to preprocess a source file
reduce_v4_unroll_last_warp.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/build.make 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/reduce_v4_unroll_last_warp.cu.i
.PHONY : reduce_v4_unroll_last_warp.cu.i

reduce_v4_unroll_last_warp.s: reduce_v4_unroll_last_warp.cu.s
.PHONY : reduce_v4_unroll_last_warp.s

# target to generate assembly for a file
reduce_v4_unroll_last_warp.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/build.make 3_reduce/CMakeFiles/reduce_v4_unroll_last_warp.dir/reduce_v4_unroll_last_warp.cu.s
.PHONY : reduce_v4_unroll_last_warp.cu.s

reduce_v5_completely_unroll.o: reduce_v5_completely_unroll.cu.o
.PHONY : reduce_v5_completely_unroll.o

# target to build an object file
reduce_v5_completely_unroll.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/build.make 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/reduce_v5_completely_unroll.cu.o
.PHONY : reduce_v5_completely_unroll.cu.o

reduce_v5_completely_unroll.i: reduce_v5_completely_unroll.cu.i
.PHONY : reduce_v5_completely_unroll.i

# target to preprocess a source file
reduce_v5_completely_unroll.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/build.make 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/reduce_v5_completely_unroll.cu.i
.PHONY : reduce_v5_completely_unroll.cu.i

reduce_v5_completely_unroll.s: reduce_v5_completely_unroll.cu.s
.PHONY : reduce_v5_completely_unroll.s

# target to generate assembly for a file
reduce_v5_completely_unroll.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/build.make 3_reduce/CMakeFiles/reduce_v5_completely_unroll.dir/reduce_v5_completely_unroll.cu.s
.PHONY : reduce_v5_completely_unroll.cu.s

reduce_v6_multi_add.o: reduce_v6_multi_add.cu.o
.PHONY : reduce_v6_multi_add.o

# target to build an object file
reduce_v6_multi_add.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/build.make 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/reduce_v6_multi_add.cu.o
.PHONY : reduce_v6_multi_add.cu.o

reduce_v6_multi_add.i: reduce_v6_multi_add.cu.i
.PHONY : reduce_v6_multi_add.i

# target to preprocess a source file
reduce_v6_multi_add.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/build.make 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/reduce_v6_multi_add.cu.i
.PHONY : reduce_v6_multi_add.cu.i

reduce_v6_multi_add.s: reduce_v6_multi_add.cu.s
.PHONY : reduce_v6_multi_add.s

# target to generate assembly for a file
reduce_v6_multi_add.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/build.make 3_reduce/CMakeFiles/reduce_v6_multi_add.dir/reduce_v6_multi_add.cu.s
.PHONY : reduce_v6_multi_add.cu.s

reduce_v7_shuffle.o: reduce_v7_shuffle.cu.o
.PHONY : reduce_v7_shuffle.o

# target to build an object file
reduce_v7_shuffle.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build.make 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o
.PHONY : reduce_v7_shuffle.cu.o

reduce_v7_shuffle.i: reduce_v7_shuffle.cu.i
.PHONY : reduce_v7_shuffle.i

# target to preprocess a source file
reduce_v7_shuffle.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build.make 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.i
.PHONY : reduce_v7_shuffle.cu.i

reduce_v7_shuffle.s: reduce_v7_shuffle.cu.s
.PHONY : reduce_v7_shuffle.s

# target to generate assembly for a file
reduce_v7_shuffle.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build.make 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.s
.PHONY : reduce_v7_shuffle.cu.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... block_all_reduce"
	@echo "... block_all_reduce_v0"
	@echo "... block_all_reduce_v1"
	@echo "... reduce_v0_baseline"
	@echo "... reduce_v1_no_divergence_branch"
	@echo "... reduce_v2_no_bank_conflict"
	@echo "... reduce_v3_add_during_load"
	@echo "... reduce_v4_unroll_last_warp"
	@echo "... reduce_v5_completely_unroll"
	@echo "... reduce_v6_multi_add"
	@echo "... reduce_v7_shuffle"
	@echo "... block_all_reduce.o"
	@echo "... block_all_reduce.i"
	@echo "... block_all_reduce.s"
	@echo "... block_all_reduce_v0.o"
	@echo "... block_all_reduce_v0.i"
	@echo "... block_all_reduce_v0.s"
	@echo "... block_all_reduce_v1.o"
	@echo "... block_all_reduce_v1.i"
	@echo "... block_all_reduce_v1.s"
	@echo "... reduce_v0_baseline.o"
	@echo "... reduce_v0_baseline.i"
	@echo "... reduce_v0_baseline.s"
	@echo "... reduce_v1_no_divergence_branch.o"
	@echo "... reduce_v1_no_divergence_branch.i"
	@echo "... reduce_v1_no_divergence_branch.s"
	@echo "... reduce_v2_no_bank_conflict.o"
	@echo "... reduce_v2_no_bank_conflict.i"
	@echo "... reduce_v2_no_bank_conflict.s"
	@echo "... reduce_v3_add_during_load.o"
	@echo "... reduce_v3_add_during_load.i"
	@echo "... reduce_v3_add_during_load.s"
	@echo "... reduce_v4_unroll_last_warp.o"
	@echo "... reduce_v4_unroll_last_warp.i"
	@echo "... reduce_v4_unroll_last_warp.s"
	@echo "... reduce_v5_completely_unroll.o"
	@echo "... reduce_v5_completely_unroll.i"
	@echo "... reduce_v5_completely_unroll.s"
	@echo "... reduce_v6_multi_add.o"
	@echo "... reduce_v6_multi_add.i"
	@echo "... reduce_v6_multi_add.s"
	@echo "... reduce_v7_shuffle.o"
	@echo "... reduce_v7_shuffle.i"
	@echo "... reduce_v7_shuffle.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

