# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

# Include any dependencies generated for this target.
include 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/compiler_depend.make

# Include the progress variables for this target.
include 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/progress.make

# Include the compile flags for this target's objects.
include 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/flags.make

3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/flags.make
3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o: ../3_reduce/reduce_v7_shuffle.cu
3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CUDA object 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/3_reduce && /usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o -MF CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o.d -x cu -dc /mnt/g/project/CUDA/cuda_practice/3_reduce/reduce_v7_shuffle.cu -o CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o

3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CUDA source to CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CUDA source to assembly CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

# Object files for target reduce_v7_shuffle
reduce_v7_shuffle_OBJECTS = \
"CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o"

# External object files for target reduce_v7_shuffle
reduce_v7_shuffle_EXTERNAL_OBJECTS =

3_reduce/CMakeFiles/reduce_v7_shuffle.dir/cmake_device_link.o: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o
3_reduce/CMakeFiles/reduce_v7_shuffle.dir/cmake_device_link.o: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build.make
3_reduce/CMakeFiles/reduce_v7_shuffle.dir/cmake_device_link.o: /usr/local/cuda/lib64/libcudart.so
3_reduce/CMakeFiles/reduce_v7_shuffle.dir/cmake_device_link.o: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/dlink.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CUDA device code CMakeFiles/reduce_v7_shuffle.dir/cmake_device_link.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/3_reduce && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/reduce_v7_shuffle.dir/dlink.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/cmake_device_link.o
.PHONY : 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build

# Object files for target reduce_v7_shuffle
reduce_v7_shuffle_OBJECTS = \
"CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o"

# External object files for target reduce_v7_shuffle
reduce_v7_shuffle_EXTERNAL_OBJECTS =

3_reduce/reduce_v7_shuffle: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/reduce_v7_shuffle.cu.o
3_reduce/reduce_v7_shuffle: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build.make
3_reduce/reduce_v7_shuffle: /usr/local/cuda/lib64/libcudart.so
3_reduce/reduce_v7_shuffle: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/cmake_device_link.o
3_reduce/reduce_v7_shuffle: 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CUDA executable reduce_v7_shuffle"
	cd /mnt/g/project/CUDA/cuda_practice/build/3_reduce && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/reduce_v7_shuffle.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build: 3_reduce/reduce_v7_shuffle
.PHONY : 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/build

3_reduce/CMakeFiles/reduce_v7_shuffle.dir/clean:
	cd /mnt/g/project/CUDA/cuda_practice/build/3_reduce && $(CMAKE_COMMAND) -P CMakeFiles/reduce_v7_shuffle.dir/cmake_clean.cmake
.PHONY : 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/clean

3_reduce/CMakeFiles/reduce_v7_shuffle.dir/depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/g/project/CUDA/cuda_practice /mnt/g/project/CUDA/cuda_practice/3_reduce /mnt/g/project/CUDA/cuda_practice/build /mnt/g/project/CUDA/cuda_practice/build/3_reduce /mnt/g/project/CUDA/cuda_practice/build/3_reduce/CMakeFiles/reduce_v7_shuffle.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : 3_reduce/CMakeFiles/reduce_v7_shuffle.dir/depend

