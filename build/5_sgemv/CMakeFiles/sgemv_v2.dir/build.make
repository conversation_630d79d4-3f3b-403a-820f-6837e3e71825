# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

# Include any dependencies generated for this target.
include 5_sgemv/CMakeFiles/sgemv_v2.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include 5_sgemv/CMakeFiles/sgemv_v2.dir/compiler_depend.make

# Include the progress variables for this target.
include 5_sgemv/CMakeFiles/sgemv_v2.dir/progress.make

# Include the compile flags for this target's objects.
include 5_sgemv/CMakeFiles/sgemv_v2.dir/flags.make

5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o: 5_sgemv/CMakeFiles/sgemv_v2.dir/flags.make
5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o: ../5_sgemv/sgemv_v2.cu
5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o: 5_sgemv/CMakeFiles/sgemv_v2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CUDA object 5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/5_sgemv && /usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT 5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o -MF CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o.d -x cu -dc /mnt/g/project/CUDA/cuda_practice/5_sgemv/sgemv_v2.cu -o CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o

5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CUDA source to CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CUDA source to assembly CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

# Object files for target sgemv_v2
sgemv_v2_OBJECTS = \
"CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o"

# External object files for target sgemv_v2
sgemv_v2_EXTERNAL_OBJECTS =

5_sgemv/CMakeFiles/sgemv_v2.dir/cmake_device_link.o: 5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o
5_sgemv/CMakeFiles/sgemv_v2.dir/cmake_device_link.o: 5_sgemv/CMakeFiles/sgemv_v2.dir/build.make
5_sgemv/CMakeFiles/sgemv_v2.dir/cmake_device_link.o: /usr/local/cuda/lib64/libcudart.so
5_sgemv/CMakeFiles/sgemv_v2.dir/cmake_device_link.o: /usr/local/cuda/lib64/libcublas.so
5_sgemv/CMakeFiles/sgemv_v2.dir/cmake_device_link.o: 5_sgemv/CMakeFiles/sgemv_v2.dir/dlink.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CUDA device code CMakeFiles/sgemv_v2.dir/cmake_device_link.o"
	cd /mnt/g/project/CUDA/cuda_practice/build/5_sgemv && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sgemv_v2.dir/dlink.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
5_sgemv/CMakeFiles/sgemv_v2.dir/build: 5_sgemv/CMakeFiles/sgemv_v2.dir/cmake_device_link.o
.PHONY : 5_sgemv/CMakeFiles/sgemv_v2.dir/build

# Object files for target sgemv_v2
sgemv_v2_OBJECTS = \
"CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o"

# External object files for target sgemv_v2
sgemv_v2_EXTERNAL_OBJECTS =

5_sgemv/sgemv_v2: 5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o
5_sgemv/sgemv_v2: 5_sgemv/CMakeFiles/sgemv_v2.dir/build.make
5_sgemv/sgemv_v2: /usr/local/cuda/lib64/libcudart.so
5_sgemv/sgemv_v2: /usr/local/cuda/lib64/libcublas.so
5_sgemv/sgemv_v2: 5_sgemv/CMakeFiles/sgemv_v2.dir/cmake_device_link.o
5_sgemv/sgemv_v2: 5_sgemv/CMakeFiles/sgemv_v2.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/g/project/CUDA/cuda_practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CUDA executable sgemv_v2"
	cd /mnt/g/project/CUDA/cuda_practice/build/5_sgemv && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sgemv_v2.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
5_sgemv/CMakeFiles/sgemv_v2.dir/build: 5_sgemv/sgemv_v2
.PHONY : 5_sgemv/CMakeFiles/sgemv_v2.dir/build

5_sgemv/CMakeFiles/sgemv_v2.dir/clean:
	cd /mnt/g/project/CUDA/cuda_practice/build/5_sgemv && $(CMAKE_COMMAND) -P CMakeFiles/sgemv_v2.dir/cmake_clean.cmake
.PHONY : 5_sgemv/CMakeFiles/sgemv_v2.dir/clean

5_sgemv/CMakeFiles/sgemv_v2.dir/depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/g/project/CUDA/cuda_practice /mnt/g/project/CUDA/cuda_practice/5_sgemv /mnt/g/project/CUDA/cuda_practice/build /mnt/g/project/CUDA/cuda_practice/build/5_sgemv /mnt/g/project/CUDA/cuda_practice/build/5_sgemv/CMakeFiles/sgemv_v2.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : 5_sgemv/CMakeFiles/sgemv_v2.dir/depend

