# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/g/project/CUDA/cuda_practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/g/project/CUDA/cuda_practice/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles /mnt/g/project/CUDA/cuda_practice/build/5_sgemv//CMakeFiles/progress.marks
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 5_sgemv/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/g/project/CUDA/cuda_practice/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 5_sgemv/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 5_sgemv/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 5_sgemv/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
5_sgemv/CMakeFiles/sgemv_v0.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 5_sgemv/CMakeFiles/sgemv_v0.dir/rule
.PHONY : 5_sgemv/CMakeFiles/sgemv_v0.dir/rule

# Convenience name for target.
sgemv_v0: 5_sgemv/CMakeFiles/sgemv_v0.dir/rule
.PHONY : sgemv_v0

# fast build rule for target.
sgemv_v0/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v0.dir/build.make 5_sgemv/CMakeFiles/sgemv_v0.dir/build
.PHONY : sgemv_v0/fast

# Convenience name for target.
5_sgemv/CMakeFiles/sgemv_v1.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 5_sgemv/CMakeFiles/sgemv_v1.dir/rule
.PHONY : 5_sgemv/CMakeFiles/sgemv_v1.dir/rule

# Convenience name for target.
sgemv_v1: 5_sgemv/CMakeFiles/sgemv_v1.dir/rule
.PHONY : sgemv_v1

# fast build rule for target.
sgemv_v1/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v1.dir/build.make 5_sgemv/CMakeFiles/sgemv_v1.dir/build
.PHONY : sgemv_v1/fast

# Convenience name for target.
5_sgemv/CMakeFiles/sgemv_v2.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 5_sgemv/CMakeFiles/sgemv_v2.dir/rule
.PHONY : 5_sgemv/CMakeFiles/sgemv_v2.dir/rule

# Convenience name for target.
sgemv_v2: 5_sgemv/CMakeFiles/sgemv_v2.dir/rule
.PHONY : sgemv_v2

# fast build rule for target.
sgemv_v2/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v2.dir/build.make 5_sgemv/CMakeFiles/sgemv_v2.dir/build
.PHONY : sgemv_v2/fast

# Convenience name for target.
5_sgemv/CMakeFiles/sgemv.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 5_sgemv/CMakeFiles/sgemv.dir/rule
.PHONY : 5_sgemv/CMakeFiles/sgemv.dir/rule

# Convenience name for target.
sgemv: 5_sgemv/CMakeFiles/sgemv.dir/rule
.PHONY : sgemv

# fast build rule for target.
sgemv/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv.dir/build.make 5_sgemv/CMakeFiles/sgemv.dir/build
.PHONY : sgemv/fast

# Convenience name for target.
5_sgemv/CMakeFiles/ComplexHalfGemv.dir/rule:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/rule
.PHONY : 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/rule

# Convenience name for target.
ComplexHalfGemv: 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/rule
.PHONY : ComplexHalfGemv

# fast build rule for target.
ComplexHalfGemv/fast:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/build.make 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/build
.PHONY : ComplexHalfGemv/fast

ComplexHalfGemv.o: ComplexHalfGemv.cu.o
.PHONY : ComplexHalfGemv.o

# target to build an object file
ComplexHalfGemv.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/build.make 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/ComplexHalfGemv.cu.o
.PHONY : ComplexHalfGemv.cu.o

ComplexHalfGemv.i: ComplexHalfGemv.cu.i
.PHONY : ComplexHalfGemv.i

# target to preprocess a source file
ComplexHalfGemv.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/build.make 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/ComplexHalfGemv.cu.i
.PHONY : ComplexHalfGemv.cu.i

ComplexHalfGemv.s: ComplexHalfGemv.cu.s
.PHONY : ComplexHalfGemv.s

# target to generate assembly for a file
ComplexHalfGemv.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/build.make 5_sgemv/CMakeFiles/ComplexHalfGemv.dir/ComplexHalfGemv.cu.s
.PHONY : ComplexHalfGemv.cu.s

sgemv.o: sgemv.cu.o
.PHONY : sgemv.o

# target to build an object file
sgemv.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv.dir/build.make 5_sgemv/CMakeFiles/sgemv.dir/sgemv.cu.o
.PHONY : sgemv.cu.o

sgemv.i: sgemv.cu.i
.PHONY : sgemv.i

# target to preprocess a source file
sgemv.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv.dir/build.make 5_sgemv/CMakeFiles/sgemv.dir/sgemv.cu.i
.PHONY : sgemv.cu.i

sgemv.s: sgemv.cu.s
.PHONY : sgemv.s

# target to generate assembly for a file
sgemv.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv.dir/build.make 5_sgemv/CMakeFiles/sgemv.dir/sgemv.cu.s
.PHONY : sgemv.cu.s

sgemv_v0.o: sgemv_v0.cu.o
.PHONY : sgemv_v0.o

# target to build an object file
sgemv_v0.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v0.dir/build.make 5_sgemv/CMakeFiles/sgemv_v0.dir/sgemv_v0.cu.o
.PHONY : sgemv_v0.cu.o

sgemv_v0.i: sgemv_v0.cu.i
.PHONY : sgemv_v0.i

# target to preprocess a source file
sgemv_v0.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v0.dir/build.make 5_sgemv/CMakeFiles/sgemv_v0.dir/sgemv_v0.cu.i
.PHONY : sgemv_v0.cu.i

sgemv_v0.s: sgemv_v0.cu.s
.PHONY : sgemv_v0.s

# target to generate assembly for a file
sgemv_v0.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v0.dir/build.make 5_sgemv/CMakeFiles/sgemv_v0.dir/sgemv_v0.cu.s
.PHONY : sgemv_v0.cu.s

sgemv_v1.o: sgemv_v1.cu.o
.PHONY : sgemv_v1.o

# target to build an object file
sgemv_v1.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v1.dir/build.make 5_sgemv/CMakeFiles/sgemv_v1.dir/sgemv_v1.cu.o
.PHONY : sgemv_v1.cu.o

sgemv_v1.i: sgemv_v1.cu.i
.PHONY : sgemv_v1.i

# target to preprocess a source file
sgemv_v1.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v1.dir/build.make 5_sgemv/CMakeFiles/sgemv_v1.dir/sgemv_v1.cu.i
.PHONY : sgemv_v1.cu.i

sgemv_v1.s: sgemv_v1.cu.s
.PHONY : sgemv_v1.s

# target to generate assembly for a file
sgemv_v1.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v1.dir/build.make 5_sgemv/CMakeFiles/sgemv_v1.dir/sgemv_v1.cu.s
.PHONY : sgemv_v1.cu.s

sgemv_v2.o: sgemv_v2.cu.o
.PHONY : sgemv_v2.o

# target to build an object file
sgemv_v2.cu.o:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v2.dir/build.make 5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.o
.PHONY : sgemv_v2.cu.o

sgemv_v2.i: sgemv_v2.cu.i
.PHONY : sgemv_v2.i

# target to preprocess a source file
sgemv_v2.cu.i:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v2.dir/build.make 5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.i
.PHONY : sgemv_v2.cu.i

sgemv_v2.s: sgemv_v2.cu.s
.PHONY : sgemv_v2.s

# target to generate assembly for a file
sgemv_v2.cu.s:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(MAKE) $(MAKESILENT) -f 5_sgemv/CMakeFiles/sgemv_v2.dir/build.make 5_sgemv/CMakeFiles/sgemv_v2.dir/sgemv_v2.cu.s
.PHONY : sgemv_v2.cu.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... ComplexHalfGemv"
	@echo "... sgemv"
	@echo "... sgemv_v0"
	@echo "... sgemv_v1"
	@echo "... sgemv_v2"
	@echo "... ComplexHalfGemv.o"
	@echo "... ComplexHalfGemv.i"
	@echo "... ComplexHalfGemv.s"
	@echo "... sgemv.o"
	@echo "... sgemv.i"
	@echo "... sgemv.s"
	@echo "... sgemv_v0.o"
	@echo "... sgemv_v0.i"
	@echo "... sgemv_v0.s"
	@echo "... sgemv_v1.o"
	@echo "... sgemv_v1.i"
	@echo "... sgemv_v1.s"
	@echo "... sgemv_v2.o"
	@echo "... sgemv_v2.i"
	@echo "... sgemv_v2.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /mnt/g/project/CUDA/cuda_practice/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

