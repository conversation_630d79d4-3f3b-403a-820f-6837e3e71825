[{"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/bin/c++  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -g -o CMakeFiles/gemm_common.dir/common/REF_MMult.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/REF_MMult.cpp", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/REF_MMult.cpp"}, {"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/bin/c++  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -g -o CMakeFiles/gemm_common.dir/common/copy_matrix.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/copy_matrix.cpp", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/copy_matrix.cpp"}, {"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/bin/c++  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -g -o CMakeFiles/gemm_common.dir/common/compare_matrices.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/compare_matrices.cpp", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/compare_matrices.cpp"}, {"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/bin/c++  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -g -o CMakeFiles/gemm_common.dir/common/random_matrix.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/random_matrix.cpp", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/random_matrix.cpp"}, {"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/bin/c++  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -g -o CMakeFiles/gemm_common.dir/common/dclock.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/dclock.cpp", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/dclock.cpp"}, {"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/bin/c++  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -g -o CMakeFiles/gemm_common.dir/common/cpu_sgemm.cpp.o -c /mnt/g/project/CUDA/cuda_practice/4_sgemm/common/cpu_sgemm.cpp", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/common/cpu_sgemm.cpp"}, {"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -isystem=/usr/local/cuda/include -g --generate-code=arch=compute_89,code=[compute_89,sm_89] -G -x cu -dc /mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v0_naive.cu -o CMakeFiles/sgemm_v0_naive.dir/sgemm_v0_naive.cu.o", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v0_naive.cu"}, {"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -isystem=/usr/local/cuda/include -g --generate-code=arch=compute_89,code=[compute_89,sm_89] -G -x cu -dc /mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v1.cu -o CMakeFiles/sgemm_v1.dir/sgemm_v1.cu.o", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v1.cu"}, {"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -isystem=/usr/local/cuda/include -g --generate-code=arch=compute_89,code=[compute_89,sm_89] -G -x cu -dc /mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v2.cu -o CMakeFiles/sgemm_v2.dir/sgemm_v2.cu.o", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v2.cu"}, {"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -isystem=/usr/local/cuda/include -g --generate-code=arch=compute_89,code=[compute_89,sm_89] -G -x cu -dc /mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v3.cu -o CMakeFiles/sgemm_v3.dir/sgemm_v3.cu.o", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v3.cu"}, {"directory": "/mnt/g/project/CUDA/cuda_practice/build/4_sgemm", "command": "/usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler  -I/mnt/g/project/CUDA/cuda_practice/4_sgemm -I/mnt/g/project/CUDA/cuda_practice/4_sgemm/common -isystem=/usr/local/cuda/include -g --generate-code=arch=compute_89,code=[compute_89,sm_89] -G -x cu -dc /mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v4.cu -o CMakeFiles/sgemm_v4.dir/sgemm_v4.cu.o", "file": "/mnt/g/project/CUDA/cuda_practice/4_sgemm/sgemm_v4.cu"}]