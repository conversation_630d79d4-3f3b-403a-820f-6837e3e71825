#include <cuda_runtime.h>
#include <common/tester.h>
#include <common/common.h>

using namespace nvcuda;

#define WARP_SIZE 32

#define CP_ASYNC_COMMIT_GROUP() asm volatile("cp.async.commit_group;\n" ::)
#define CP_ASYNC_WAIT_GROUP(n) asm volatile("cp.async.wait_group %0;\n" ::"n"(n))
#define CP_ASYNC_CG(dst, src, bytes) asm volatile("cp.async.cg.shared.global.L2::128B [%0], [%1], %2;\n" ::"r"(dst), "l"(src), "n"(bytes))

// 256x128, stages, mma4x2, warp4x4(64,64,16)
template<const int WMMA_M=16, 
         const int WMMA_N=16, 
         const int WMMA_K=16, 
         const int WMMA_TILE_M=4, 
         const int WMMA_TILE_N=2, 
         const int WARP_TILE_M=4, 
         const int WARP_TILE_N=4,
         const int WARP_TILE_K=1, 
         const int A_PAD=0, 
         const int B_PAD=0, 
         const int K_STAGE=2,
         const bool BLOCK_SWIZZLE=false>
__global__ void __launch_bounds__(256) 
hgemm_wmma_m16n16k16_mma4x2_warp4x4_stages_dsmem_kernel(
  half* A, half* B, half* C, int M, int N, int K) {
  // 256 threads(8 warps) per block.
  // const int bx = blockIdx.x;
  // BLOCK_SWIZZLE 0/1 control use block swizzle or not.
  const int bx = ((int) BLOCK_SWIZZLE) * blockIdx.z * gridDim.x + blockIdx.x;
  const int by = blockIdx.y;
  const int NUM_K_TILES = div_ceil(K, WMMA_K * WARP_TILE_K);
  constexpr int BM = WMMA_M * WMMA_TILE_M * WARP_TILE_M; // 16x4*4=256
  constexpr int BN = WMMA_N * WMMA_TILE_N * WARP_TILE_N; // 16x2*4=128
  constexpr int BK = WMMA_K * WARP_TILE_K; // 16*2=32
  // s2: 2*128*(32)*2=16KB, 2*32*(128+16)*2=18KB, ~42KB
  // s3: 3*128*(32)*2=24KB, 3*32*(128+16)*2=27KB, ~51KB
  // s4: 4*128*(32)*2=32KB, 4*32*(128+16)*2=36KB, ~68KB
  // s4: 5*128*(32)*2=40KB, 5*32*(128+16)*2=45KB, ~85KB
  extern __shared__ half smem[]; 
  half* s_a = smem;
  half* s_b = smem + K_STAGE * BM * (BK + A_PAD);
  constexpr int s_a_stage_offset = BM * (BK + A_PAD);
  constexpr int s_b_stage_offset = BK * (BN + B_PAD);

  const int tid = threadIdx.y * blockDim.x + threadIdx.x;
  const int warp_id = tid / WARP_SIZE; // 0~7 warp_id within block
  const int warp_m = warp_id / 2; // 0,1,2,3
  const int warp_n = warp_id % 2; // 0,1
  
  // 先计算shared memory中的索引
  // tid和需要加载的smem s_a[BM][BK] 之间的索引关系 BM=256 BK=32 按行读取 A行主序
  // 对于s_a每行16个数据，每个线程读取16个，需要1个线程；总共256行，刚好256线程
  int load_smem_a_m = tid; // row 0~255
  int load_smem_a_k = 0; // col 0,16
  // tid和需要加载的smem s_b[BK][BN] 之间的索引关系 BK=16 BN=128 按行读取 B行主序
  // 对于s_b每行128个数据，每个线程读8个数据，需要16个线程；总共16行，需要16x16=256个线程
  int load_smem_b_k = tid / 16; // row 0~15
  int load_smem_b_n = (tid % 16) * 8; // col 0,8,...,120
  // 再计算全局内存中的索引
  // 要加载到s_a中的元素对应到A全局内存中的行数 每个block负责出C中大小为BM*BN的块
  int load_gmem_a_m = by * BM + load_smem_a_m; // global row of a and c
  int load_gmem_b_n = bx * BN + load_smem_b_n; // global col of b and c
  if (load_gmem_a_m >= M || load_gmem_b_n >= N) return;

  wmma::fragment<wmma::accumulator, WMMA_M, WMMA_N, WMMA_K, half> 
  C_frag[WARP_TILE_M][WARP_TILE_N];
  
  #pragma unroll
  for (int i = 0; i < WARP_TILE_M; ++i) {
    #pragma unroll
    for (int j = 0; j < WARP_TILE_N; ++j) {
      wmma::fill_fragment(C_frag[i][j], 0.0);
    }
  }

  // only cvta smem base ptr once for cp.async.
  uint32_t smem_a_base_ptr = __cvta_generic_to_shared(s_a);
  uint32_t smem_b_base_ptr = __cvta_generic_to_shared(s_b);

  #pragma unroll
  for (int k = 0; k < (K_STAGE - 1); ++k) { // 0, 1
    // k * WMMA_K, WMMA_K=16 -> (k << 4)
    int load_gmem_a_k = k * (WMMA_K * WARP_TILE_K) + load_smem_a_k; // global col of a
    int load_gmem_a_addr = load_gmem_a_m * K + load_gmem_a_k;
    int load_gmem_b_k = k * (WMMA_K * WARP_TILE_K) + load_smem_b_k; // global row of b
    int load_gmem_b_addr = load_gmem_b_k * N + load_gmem_b_n; 
    
    uint32_t load_smem_a_ptr = (
      smem_a_base_ptr + (k * s_a_stage_offset + 
                         load_smem_a_m * (BK + A_PAD) + 
                         load_smem_a_k) * sizeof(half)
    );

    uint32_t load_smem_b_ptr = (
      smem_b_base_ptr + (k * s_b_stage_offset + 
                         load_smem_b_k * (BN + B_PAD) + 
                         load_smem_b_n) * sizeof(half)
    );

    CP_ASYNC_CG(load_smem_a_ptr,      &A[load_gmem_a_addr],     16);
    CP_ASYNC_CG(load_smem_a_ptr + 16, &A[load_gmem_a_addr + 8], 16);
    CP_ASYNC_CG(load_smem_b_ptr,      &B[load_gmem_b_addr],     16);   

    CP_ASYNC_COMMIT_GROUP();
  }

  CP_ASYNC_WAIT_GROUP(K_STAGE-2); // s2->0, s3->1, s4->2
  __syncthreads(); 

  #pragma unroll
  for (int k = (K_STAGE - 1); k < NUM_K_TILES; k++) { 
    // s2/4 can use bitwise ops but s3 can not, so, we use mod
    // ops for all stages kernel. s2: (k + 1)&1, s4: (k + 1)&3
    // s3: (k + 1) % 3
    int smem_sel = (k + 1) % K_STAGE; // s3 k 2->0, k 3->1, k 4->2...
    int smem_sel_next = k % K_STAGE;  // s3 k 2->2, k 3->0, k 4->1...

    // k * WMMA_K, WMMA_K=16 -> (k << 4)
    int load_gmem_a_k = k * (WMMA_K * WARP_TILE_K) + load_smem_a_k; // global col of a
    int load_gmem_a_addr = load_gmem_a_m * K + load_gmem_a_k;
    int load_gmem_b_k = k * (WMMA_K * WARP_TILE_K) + load_smem_b_k; // global row of b
    int load_gmem_b_addr = load_gmem_b_k * N + load_gmem_b_n; 

    // load stage 2, k start from 2
    uint32_t load_smem_a_ptr = (
      smem_a_base_ptr + (smem_sel_next * s_a_stage_offset + 
                         load_smem_a_m * (BK + A_PAD) + 
                         load_smem_a_k) * sizeof(half)
    );

    uint32_t load_smem_b_ptr = (
      smem_b_base_ptr + (smem_sel_next * s_b_stage_offset + 
                         load_smem_b_k * (BN + B_PAD) + 
                         load_smem_b_n) * sizeof(half)
    );

    CP_ASYNC_CG(load_smem_a_ptr,      &A[load_gmem_a_addr],     16);
    CP_ASYNC_CG(load_smem_a_ptr + 16, &A[load_gmem_a_addr + 8], 16);
    CP_ASYNC_CG(load_smem_b_ptr,      &B[load_gmem_b_addr],     16);   

    CP_ASYNC_COMMIT_GROUP();
    
    #pragma unroll
    for (int warp_k = 0; warp_k < WARP_TILE_K; ++warp_k) {
      wmma::fragment<wmma::matrix_a, WMMA_M, WMMA_N, WMMA_K, half, 
                     wmma::row_major> A_frag[WARP_TILE_M];
      wmma::fragment<wmma::matrix_b, WMMA_M, WMMA_N, WMMA_K, half, 
                     wmma::row_major> B_frag[WARP_TILE_N];
      const int warp_smem_k = warp_k * WMMA_K; // 0,16

      // compute stage 0
      #pragma unroll
      for (int i = 0; i < WARP_TILE_M; ++i) {
        // load 2 tiles -> reg, smem a -> frags a, warp_m 0~3
        int warp_smem_a_m = warp_m * (WMMA_M * WARP_TILE_M) + i * WMMA_M;
        half* load_smem_a_frag_ptr = (s_a + smem_sel * s_a_stage_offset + 
                                      warp_smem_a_m * (BK + A_PAD) + 
                                      warp_smem_k);
        wmma::load_matrix_sync(A_frag[i], load_smem_a_frag_ptr, BK + A_PAD); 
      }

      #pragma unroll
      for (int j = 0; j < WARP_TILE_N; ++j) {
        // load 4 tiles -> reg, smem b -> frags b, warp_n 0~2
        int warp_smem_b_n = warp_n * (WMMA_N * WARP_TILE_N) + j * WMMA_N;
        half* load_smem_b_frag_ptr = (s_b + smem_sel * s_b_stage_offset + 
                                      warp_smem_k * (BN + B_PAD) + 
                                      warp_smem_b_n);
        wmma::load_matrix_sync(B_frag[j], load_smem_b_frag_ptr, BN + B_PAD);
      }

      #pragma unroll
      for (int i = 0; i < WARP_TILE_M; ++i) {
        #pragma unroll
        for (int j = 0; j < WARP_TILE_N; ++j) {
          wmma::mma_sync(C_frag[i][j], A_frag[i], B_frag[j], C_frag[i][j]);
        }
      }
    }
    
    CP_ASYNC_WAIT_GROUP(K_STAGE-2);
    __syncthreads(); 
  }
  
  // make sure all memory issues ready.
  if ((K_STAGE - 2) > 0) {
    CP_ASYNC_WAIT_GROUP(0);
    __syncthreads(); 
  }

  // processing last (K_STAGE-1) k iters.
  {
    #pragma unroll
    for (int k = 0; k < (K_STAGE - 1); k++) {
      const int stage_sel = ((NUM_K_TILES - (K_STAGE - 1) + k) % K_STAGE);

      #pragma unroll
      for (int warp_k = 0; warp_k < WARP_TILE_K; ++warp_k) {
        wmma::fragment<wmma::matrix_a, WMMA_M, WMMA_N, WMMA_K, half, 
                      wmma::row_major> A_frag[WARP_TILE_M];
        wmma::fragment<wmma::matrix_b, WMMA_M, WMMA_N, WMMA_K, half, 
                      wmma::row_major> B_frag[WARP_TILE_N];
        const int warp_smem_k = warp_k * WMMA_K; // 0,16

        // compute stage 0
        #pragma unroll
        for (int i = 0; i < WARP_TILE_M; ++i) {
          // load 2 tiles -> reg, smem a -> frags a, warp_m 0~3
          int warp_smem_a_m = warp_m * (WMMA_M * WARP_TILE_M) + i * WMMA_M;
          half* load_smem_a_frag_ptr = (s_a + stage_sel * s_a_stage_offset + 
                                        warp_smem_a_m * (BK + A_PAD) + 
                                        warp_smem_k);
          wmma::load_matrix_sync(A_frag[i], load_smem_a_frag_ptr, BK + A_PAD); 
        }

        #pragma unroll
        for (int j = 0; j < WARP_TILE_N; ++j) {
          // load 4 tiles -> reg, smem b -> frags b, warp_n 0~2
          int warp_smem_b_n = warp_n * (WMMA_N * WARP_TILE_N) + j * WMMA_N;
          half* load_smem_b_frag_ptr = (s_b + stage_sel * s_b_stage_offset + 
                                        warp_smem_k * (BN + B_PAD) + 
                                        warp_smem_b_n);
          wmma::load_matrix_sync(B_frag[j], load_smem_b_frag_ptr, BN + B_PAD);
        }

        #pragma unroll
        for (int i = 0; i < WARP_TILE_M; ++i) {
          #pragma unroll
          for (int j = 0; j < WARP_TILE_N; ++j) {
            wmma::mma_sync(C_frag[i][j], A_frag[i], B_frag[j], C_frag[i][j]);
          }
        }
      }
    }
  }

  // finally, store back to C matrix.
  #pragma unroll
  for (int i = 0; i < WARP_TILE_M; ++i) {
    #pragma unroll
    for (int j = 0; j < WARP_TILE_N; ++j) {
      const int store_gmem_a_m = by * BM + warp_m * (WMMA_M * WARP_TILE_M) + i * WMMA_M;
      const int store_gmem_a_n = bx * BN + warp_n * (WMMA_N * WARP_TILE_N) + j * WMMA_N;
      wmma::store_matrix_sync(C + store_gmem_a_m * N + store_gmem_a_n, C_frag[i][j], N, 
                              wmma::mem_row_major);
    }
  }
}

void hgemm_wmma_m16n16k16_mma4x2_warp4x4_stages_dsmem(half *A, half *B, half *C, size_t M, size_t N, size_t K){
    constexpr int WMMA_M = 16;
    constexpr int WMMA_N = 16;
    constexpr int WMMA_K = 16;
    constexpr int WMMA_TILE_M = 4;
    constexpr int WMMA_TILE_N = 2;
    constexpr int WARP_TILE_M = 4;
    constexpr int WARP_TILE_N = 4;
    constexpr int WARP_TILE_K = 1;
    constexpr int A_PAD = 0;  // 0,8,16
    constexpr int B_PAD = 16; // 0,8,16
    constexpr int NUM_THREADS= (WMMA_TILE_M * WMMA_TILE_N * WARP_SIZE); // 4 * 2 * 32 = 256
    constexpr int BM = WMMA_M * WMMA_TILE_M * WARP_TILE_M;
    constexpr int BN = WMMA_N * WMMA_TILE_N * WARP_TILE_N;
    constexpr int BK = WMMA_K * WARP_TILE_K;

    constexpr int K_STAGE = 2;
    constexpr bool BLOCK_SWIZZLE = true;

    const int smem_max_size = ((K_STAGE) * BM * (BK + A_PAD) * sizeof(half) + (K_STAGE) * BK * (BN + B_PAD) * sizeof(half));

    int stride = (N >= 2048) ? 256 : 128;
    stride = std::max(stride, BN);
    stride = (stride / BN) * BN;
    const int N_SWIZZLE = (N + (stride) - 1) / (stride);
  
    dim3 block(NUM_THREADS);
    dim3 grid((div_ceil(N, BN) + N_SWIZZLE - 1) / N_SWIZZLE, div_ceil(M, BM), N_SWIZZLE);
    // dim3 grid(div_ceil(N, BN), div_ceil(M, BM));
    hgemm_wmma_m16n16k16_mma4x2_warp4x4_stages_dsmem_kernel<WMMA_M, WMMA_N, WMMA_K, WMMA_TILE_M, WMMA_TILE_N, WARP_TILE_M, WARP_TILE_N, WARP_TILE_K, A_PAD, B_PAD, K_STAGE, BLOCK_SWIZZLE><<<grid, block, smem_max_size>>>(A, B, C, M, N, K);
}

int main(int argc, char** argv) {
    Tester tester(512, 2048, 1024, 1, 10, 100, true);
    tester.evaluate(hgemm_wmma_m16n16k16_mma4x2_warp4x4_stages_dsmem, "hgemm_wmma_m16n16k16_mma4x2_warp4x4_stages_dsmem");
    return 0;
}