# if (${CMAKE_BUILD_TYPE} MATCHES "Debug")
#     set(CUDA_NVCC_FLAGS "${CUDA_NVCC_FLAGS} -arch=sm_${CMAKE_CUDA_ARCHITECTURES} -G -g -lineinfo -Xptxas=-v -O0")
# else ()
#     set(CUDA_NVCC_FLAGS "${CUDA_NVCC_FLAGS} -gencode arch=compute_${CMAKE_CUDA_ARCHITECTURES},code=sm_${CMAKE_CUDA_ARCHITECTURES} --use_fast_math -O3")
# endif ()

add_executable(hgemm_wmma_v1_m16n16k16_naive hgemm_wmma_v1_m16n16k16_naive.cu)
target_link_libraries(hgemm_wmma_v1_m16n16k16_naive PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_wmma_v1_m16n16k16_naive PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_wmma_v1_m16n16k16_naive PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_wmma_v1_m16n16k16_naive PRIVATE -lineinfo)
endif()

add_executable(hgemm_wmma_v2_m16n16k16_mma4x2 hgemm_wmma_v2_m16n16k16_mma4x2.cu)
target_link_libraries(hgemm_wmma_v2_m16n16k16_mma4x2 PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_wmma_v2_m16n16k16_mma4x2 PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_wmma_v2_m16n16k16_mma4x2 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_wmma_v2_m16n16k16_mma4x2 PRIVATE -lineinfo)
endif()

add_executable(hgemm_wmma_v3_m16n16k16_mma4x2_warp2x4 hgemm_wmma_v3_m16n16k16_mma4x2_warp2x4.cu)
target_link_libraries(hgemm_wmma_v3_m16n16k16_mma4x2_warp2x4 PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_wmma_v3_m16n16k16_mma4x2_warp2x4 PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_wmma_v3_m16n16k16_mma4x2_warp2x4 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_wmma_v3_m16n16k16_mma4x2_warp2x4 PRIVATE -lineinfo)
endif()

add_executable(hgemm_wmma_v4_m16n16k16_mma4x2_warp2x4_dbuf_async hgemm_wmma_v4_m16n16k16_mma4x2_warp2x4_dbuf_async.cu)
target_link_libraries(hgemm_wmma_v4_m16n16k16_mma4x2_warp2x4_dbuf_async PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_wmma_v4_m16n16k16_mma4x2_warp2x4_dbuf_async PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_wmma_v4_m16n16k16_mma4x2_warp2x4_dbuf_async PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_wmma_v4_m16n16k16_mma4x2_warp2x4_dbuf_async PRIVATE -lineinfo)
endif()

add_executable(hgemm_wmma_v5_m32n8k16_mma2x4_warp2x4_dbuf_async hgemm_wmma_v5_m32n8k16_mma2x4_warp2x4_dbuf_async.cu)
target_link_libraries(hgemm_wmma_v5_m32n8k16_mma2x4_warp2x4_dbuf_async PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_wmma_v5_m32n8k16_mma2x4_warp2x4_dbuf_async PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_wmma_v5_m32n8k16_mma2x4_warp2x4_dbuf_async PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_wmma_v5_m32n8k16_mma2x4_warp2x4_dbuf_async PRIVATE -lineinfo)
endif()

add_executable(hgemm_wmma_v6_m16n16k16_mma4x2_warp2x4_stages hgemm_wmma_v6_m16n16k16_mma4x2_warp2x4_stages.cu)
target_link_libraries(hgemm_wmma_v6_m16n16k16_mma4x2_warp2x4_stages PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_wmma_v6_m16n16k16_mma4x2_warp2x4_stages PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_wmma_v6_m16n16k16_mma4x2_warp2x4_stages PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_wmma_v6_m16n16k16_mma4x2_warp2x4_stages PRIVATE -lineinfo)
endif()


add_executable(hgemm_wmma_v7_m16n16k16_mma4x2_warp2x4_stages_dsmem hgemm_wmma_v7_m16n16k16_mma4x2_warp2x4_stages_dsmem.cu)
target_link_libraries(hgemm_wmma_v7_m16n16k16_mma4x2_warp2x4_stages_dsmem PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_wmma_v7_m16n16k16_mma4x2_warp2x4_stages_dsmem PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_wmma_v7_m16n16k16_mma4x2_warp2x4_stages_dsmem PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_wmma_v7_m16n16k16_mma4x2_warp2x4_stages_dsmem PRIVATE -lineinfo)
endif()

add_executable(hgemm_wmma_v8_m16n16k16_mma4x4_warp4x4_stages_dsmem hgemm_wmma_v8_m16n16k16_mma4x4_warp4x4_stages_dsmem.cu)
target_link_libraries(hgemm_wmma_v8_m16n16k16_mma4x4_warp4x4_stages_dsmem PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_wmma_v8_m16n16k16_mma4x4_warp4x4_stages_dsmem PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_wmma_v8_m16n16k16_mma4x4_warp4x4_stages_dsmem PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_wmma_v8_m16n16k16_mma4x4_warp4x4_stages_dsmem PRIVATE -lineinfo)
endif()

add_executable(hgemm_wmma_v9_m16n16k16_mma4x2_warp4x4_stages_dsmem hgemm_wmma_v9_m16n16k16_mma4x2_warp4x4_stages_dsmem.cu)
target_link_libraries(hgemm_wmma_v9_m16n16k16_mma4x2_warp4x4_stages_dsmem PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_wmma_v9_m16n16k16_mma4x2_warp4x4_stages_dsmem PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_wmma_v9_m16n16k16_mma4x2_warp4x4_stages_dsmem PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_wmma_v9_m16n16k16_mma4x2_warp4x4_stages_dsmem PRIVATE -lineinfo)
endif()


add_executable(hgemm_mma_v1_m16n8k16_naive hgemm_mma_v1_m16n8k16_naive.cu)
target_link_libraries(hgemm_mma_v1_m16n8k16_naive PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v1_m16n8k16_naive PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v1_m16n8k16_naive PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v1_m16n8k16_naive PRIVATE -lineinfo)
endif()

add_executable(hgemm_mma_v2_m16n8k16_mma2x4_warp4x4 hgemm_mma_v2_m16n8k16_mma2x4_warp4x4.cu)
target_link_libraries(hgemm_mma_v2_m16n8k16_mma2x4_warp4x4 PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v2_m16n8k16_mma2x4_warp4x4 PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v2_m16n8k16_mma2x4_warp4x4 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v2_m16n8k16_mma2x4_warp4x4 PRIVATE -lineinfo)
endif()

add_executable(hgemm_mma_v3_m16n8k16_mma2x4_warp4x4_stages hgemm_mma_v3_m16n8k16_mma2x4_warp4x4_stages.cu)
target_link_libraries(hgemm_mma_v3_m16n8k16_mma2x4_warp4x4_stages PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v3_m16n8k16_mma2x4_warp4x4_stages PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v3_m16n8k16_mma2x4_warp4x4_stages PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v3_m16n8k16_mma2x4_warp4x4_stages PRIVATE -lineinfo)
endif()

add_executable(hgemm_mma_v4_m16n8k16_mma2x4_warp4x4_stages_dsmem hgemm_mma_v4_m16n8k16_mma2x4_warp4x4_stages_dsmem.cu)
target_link_libraries(hgemm_mma_v4_m16n8k16_mma2x4_warp4x4_stages_dsmem PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v4_m16n8k16_mma2x4_warp4x4_stages_dsmem PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v4_m16n8k16_mma2x4_warp4x4_stages_dsmem PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v4_m16n8k16_mma2x4_warp4x4_stages_dsmem PRIVATE -lineinfo)
endif()

add_executable(hgemm_mma_v5_m16n8k16_mma2x4_warp4x4x2_stages_dsmem hgemm_mma_v5_m16n8k16_mma2x4_warp4x4x2_stages_dsmem.cu)
target_link_libraries(hgemm_mma_v5_m16n8k16_mma2x4_warp4x4x2_stages_dsmem PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v5_m16n8k16_mma2x4_warp4x4x2_stages_dsmem PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v5_m16n8k16_mma2x4_warp4x4x2_stages_dsmem PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v5_m16n8k16_mma2x4_warp4x4x2_stages_dsmem PRIVATE -lineinfo)
endif()

add_executable(hgemm_mma_v6_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_x4 hgemm_mma_v6_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_x4.cu)
target_link_libraries(hgemm_mma_v6_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_x4 PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v6_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_x4 PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v6_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_x4 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v6_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_x4 PRIVATE -lineinfo)
endif()

add_executable(hgemm_mma_v7_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_rr hgemm_mma_v7_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_rr.cu)
target_link_libraries(hgemm_mma_v7_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_rr PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v7_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_rr PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v7_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_rr PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v7_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_rr PRIVATE -lineinfo)
endif()

add_executable(hgemm_mma_v8_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_swizzle hgemm_mma_v8_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_swizzle.cu)
target_link_libraries(hgemm_mma_v8_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_swizzle PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v8_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_swizzle PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v8_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_swizzle PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v8_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_swizzle PRIVATE -lineinfo)
endif()

add_executable(hgemm_mma_v9_m16n8k16_mma2x4_warp4x4_stages_dsmem_tn hgemm_mma_v9_m16n8k16_mma2x4_warp4x4_stages_dsmem_tn.cu)
target_link_libraries(hgemm_mma_v9_m16n8k16_mma2x4_warp4x4_stages_dsmem_tn PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v9_m16n8k16_mma2x4_warp4x4_stages_dsmem_tn PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v9_m16n8k16_mma2x4_warp4x4_stages_dsmem_tn PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v9_m16n8k16_mma2x4_warp4x4_stages_dsmem_tn PRIVATE -lineinfo)
endif()

add_executable(hgemm_mma_v10_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_tn_swizzle_x4 hgemm_mma_v10_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_tn_swizzle_x4.cu)
target_link_libraries(hgemm_mma_v10_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_tn_swizzle_x4 PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v10_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_tn_swizzle_x4 PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v10_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_tn_swizzle_x4 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v10_m16n8k16_mma2x4_warp4x4x2_stages_dsmem_tn_swizzle_x4 PRIVATE -lineinfo)
endif()

set(CUTLASS_PATH "/mnt/g/project/CUDA/cutlass/include")
set(CUTLASS_UTIL_PATH "/mnt/g/project/CUDA/cutlass/tools/util/include")

add_executable(hgemm_mma_v11_stages_block_swizzle_tn_cute hgemm_mma_v11_stages_block_swizzle_tn_cute.cu)
target_link_libraries(hgemm_mma_v11_stages_block_swizzle_tn_cute PRIVATE CUDA::cudart CUDA::cublas)
target_include_directories(hgemm_mma_v11_stages_block_swizzle_tn_cute PRIVATE ${CUTLASS_PATH} ${CUTLASS_UTIL_PATH} ${CMAKE_CURRENT_SOURCE_DIR})
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(hgemm_mma_v11_stages_block_swizzle_tn_cute PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(hgemm_mma_v11_stages_block_swizzle_tn_cute PRIVATE -lineinfo)
endif()