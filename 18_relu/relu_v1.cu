
#include <algorithm>
#include <cuda_runtime.h>
#include <stdio.h>
#include <stdlib.h>
#include <vector>
#include <chrono>
#include <cmath>
#include <iostream>

#define FLOAT4(value) (reinterpret_cast<float4 *>(&(value))[0])

#define checkCudaErrors(func)                                                  \
  {                                                                            \
    cudaError_t e = (func);                                                    \
    if (e != cudaSuccess)                                                      \
      printf("%s %d CUDA: %s\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
  }

// Relu x: N, y: N y=max(0,x) Vec4
// grid(N/128/4), block(128/4)
__global__ void relu_vec4(float* x, float* y, int N) {
  int idx = (blockIdx.x * blockDim.x + threadIdx.x) * 4;
  if (idx + 3 < N) {
    float4 reg_x = FLOAT4(x[idx]);
    float4 reg_y;
    reg_y.x = fmaxf(0.0f, reg_x.x);
    reg_y.y = fmaxf(0.0f, reg_x.y);
    reg_y.z = fmaxf(0.0f, reg_x.z);
    reg_y.w = fmaxf(0.0f, reg_x.w);
    FLOAT4(y[idx]) = reg_y;
  } else if (idx < N) {
    // Handle boundary cases
    for (int i = 0; i < 4 && idx + i < N; i++) {
      y[idx + i] = fmaxf(0.0f, x[idx + i]);
    }
  }
}

// CPU reference implementation
void relu_cpu(const float* x, float* y, int N) {
  for (int i = 0; i < N; i++) {
    y[i] = std::max(0.0f, x[i]);
  }
}

// Initialize vector with test data
void init_vector(float* x, int N) {
  for (int i = 0; i < N; i++) {
    x[i] = static_cast<float>(rand()) / RAND_MAX * 20.0f - 10.0f; // Range [-10, 10]
  }
}

// Verify results
bool verify_result(const float* gpu_result, const float* cpu_result, int N, float tolerance = 1e-5f) {
  int failed_count = 0;
  for (int i = 0; i < N; i++) {
    float diff = std::abs(gpu_result[i] - cpu_result[i]);
    if (diff > tolerance) {
      if (failed_count < 5) { // Only print first 5 failures
        printf("Verification failed at index %d: GPU=%.6f, CPU=%.6f, diff=%.6e\n",
               i, gpu_result[i], cpu_result[i], diff);
      }
      failed_count++;
    }
  }

  if (failed_count > 0) {
    printf("Total failed elements: %d/%d\n", failed_count, N);
    return false;
  }
  return true;
}

// Performance testing function
template<typename KernelFunc>
double benchmark_kernel(KernelFunc kernel_func, float* d_x, float* d_y,
                       int N, int num_iterations = 1000) {
  // Warm up
  for (int i = 0; i < 10; i++) {
    kernel_func(d_x, d_y, N);
  }
  checkCudaErrors(cudaDeviceSynchronize());

  // Timing
  auto start = std::chrono::high_resolution_clock::now();
  for (int i = 0; i < num_iterations; i++) {
    kernel_func(d_x, d_y, N);
  }
  checkCudaErrors(cudaDeviceSynchronize());
  auto end = std::chrono::high_resolution_clock::now();

  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
  return duration.count() / 1000.0 / num_iterations; // ms per iteration
}

int main(int argc, char **argv) {
  // Parse command line arguments
  int N = 1024 * 1024;
  if (argc >= 2) {
    N = atoi(argv[1]);
  }

  // Ensure N is aligned for float4 operations
  N = (N + 3) / 4 * 4;

  printf("ReLU v1 (Vec4) Performance Test\n");
  printf("Vector size: %d\n", N);
  printf("=================================\n");

  // Allocate host memory
  size_t size = N * sizeof(float);
  float *h_x = (float*)malloc(size);
  float *h_y_gpu = (float*)malloc(size);
  float *h_y_cpu = (float*)malloc(size);

  // Initialize input vector
  srand(42); // For reproducible results
  init_vector(h_x, N);

  // Allocate device memory
  float *d_x, *d_y;
  checkCudaErrors(cudaMalloc(&d_x, size));
  checkCudaErrors(cudaMalloc(&d_y, size));

  // Copy input data to device
  checkCudaErrors(cudaMemcpy(d_x, h_x, size, cudaMemcpyHostToDevice));

  // Compute CPU reference
  printf("Computing CPU reference...\n");
  auto cpu_start = std::chrono::high_resolution_clock::now();
  relu_cpu(h_x, h_y_cpu, N);
  auto cpu_end = std::chrono::high_resolution_clock::now();
  auto cpu_time = std::chrono::duration_cast<std::chrono::microseconds>(cpu_end - cpu_start);
  printf("CPU time: %.3f ms\n\n", cpu_time.count() / 1000.0);

  // Test GPU kernel
  printf("Testing GPU ReLU vec4 kernel...\n");
  constexpr int NUM_THREADS = 128 / 4;
  dim3 block(NUM_THREADS);
  dim3 grid((N / 4 + NUM_THREADS - 1) / NUM_THREADS);

  relu_vec4<<<grid, block>>>(d_x, d_y, N);
  checkCudaErrors(cudaGetLastError());
  checkCudaErrors(cudaDeviceSynchronize());

  checkCudaErrors(cudaMemcpy(h_y_gpu, d_y, size, cudaMemcpyDeviceToHost));

  // Verify result
  bool passed = verify_result(h_y_gpu, h_y_cpu, N);
  printf("Verification: %s\n", passed ? "PASSED" : "FAILED");

  if (passed) {
    // Benchmark performance
    double time_ms = benchmark_kernel(
        [&](float *x, float *y, int n) {
          relu_vec4<<<grid, block>>>(x, y, n);
        },
        d_x, d_y, N);

    double bandwidth = (2.0 * size) / (time_ms * 1e-3) / 1e9; // GB/s (read + write)
    printf("Performance: %.3f ms, %.2f GB/s\n", time_ms, bandwidth);
  }

  printf("\n=================================\n");
  printf("Overall result: %s\n", passed ? "PASSED" : "FAILED");

  // Cleanup
  free(h_x);
  free(h_y_gpu);
  free(h_y_cpu);
  checkCudaErrors(cudaFree(d_x));
  checkCudaErrors(cudaFree(d_y));

  return passed ? 0 : 1;
}