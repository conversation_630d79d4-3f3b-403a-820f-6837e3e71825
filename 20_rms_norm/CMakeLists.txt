cmake_minimum_required(VERSION 3.18)
project(rms_norm LANGUAGES CXX CUDA)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set CUDA standard
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

# Find required packages
find_package(CUDAToolkit REQUIRED)

# Set CUDA architecture (adjust based on your GPU)
set(CMAKE_CUDA_ARCHITECTURES 75 80 86 89)

# Create executable
add_executable(rms_norm_v0 rms_norm_v0.cu)

# Set CUDA properties
set_target_properties(rms_norm_v0 PROPERTIES
    CUDA_SEPARABLE_COMPILATION ON
    CUDA_RESOLVE_DEVICE_SYMBOLS ON
)

# Link CUDA libraries
target_link_libraries(rms_norm_v0 PRIVATE CUDA::cudart)

# Compiler-specific options
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(rms_norm_v0 PRIVATE 
        $<$<COMPILE_LANGUAGE:CUDA>:-G -g>
        $<$<COMPILE_LANGUAGE:CXX>:-g>
    )
else()
    target_compile_options(rms_norm_v0 PRIVATE 
        $<$<COMPILE_LANGUAGE:CUDA>:-O3>
        $<$<COMPILE_LANGUAGE:CXX>:-O3>
    )
endif()

# Print build information
message(STATUS "CUDA Toolkit Version: ${CUDAToolkit_VERSION}")
message(STATUS "CUDA Architectures: ${CMAKE_CUDA_ARCHITECTURES}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")

# Optional: Add install target
install(TARGETS rms_norm_v0
    RUNTIME DESTINATION bin
)
