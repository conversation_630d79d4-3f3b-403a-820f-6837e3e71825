cmake_minimum_required(VERSION 3.18)
project(spmm LANGUAGES CXX CUDA)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set CUDA standard
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

# Find required packages
find_package(CUDAToolkit REQUIRED)

# Set CUDA architecture (adjust based on your GPU)
set(CMAKE_CUDA_ARCHITECTURES 75 80 86 89)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# Try to find Sputnik library
find_path(SPUTNIK_INCLUDE_DIR
    NAMES sputnik/sputnik.h
    PATHS
        /usr/local/include
        /usr/include
        ${CMAKE_CURRENT_SOURCE_DIR}/third_party/sputnik
        ${CMAKE_CURRENT_SOURCE_DIR}/../third_party/sputnik
        ${CMAKE_CURRENT_SOURCE_DIR}/sputnik
    DOC "Path to Sputnik headers"
)

find_library(SPUTNIK_LIBRARY
    NAMES sputnik libsputnik
    PATHS
        /usr/local/lib
        /usr/lib
        ${CMAKE_CURRENT_SOURCE_DIR}/third_party/sputnik/lib
        ${CMAKE_CURRENT_SOURCE_DIR}/../third_party/sputnik/lib
        ${CMAKE_CURRENT_SOURCE_DIR}/sputnik/lib
    DOC "Path to Sputnik library"
)

# Create the executable
add_executable(spmm spmm.cu)

# Set CUDA properties
set_target_properties(spmm PROPERTIES
    CUDA_SEPARABLE_COMPILATION ON
    CUDA_RESOLVE_DEVICE_SYMBOLS ON
)

# Link CUDA libraries
target_link_libraries(spmm PRIVATE 
    CUDA::cudart 
    CUDA::cusparse
)

# Handle Sputnik dependency
if(SPUTNIK_INCLUDE_DIR AND SPUTNIK_LIBRARY)
    message(STATUS "Found Sputnik: ${SPUTNIK_LIBRARY}")
    target_include_directories(spmm PRIVATE ${SPUTNIK_INCLUDE_DIR})
    target_link_libraries(spmm PRIVATE ${SPUTNIK_LIBRARY})
    target_compile_definitions(spmm PRIVATE HAVE_SPUTNIK)
else()
    message(WARNING "Sputnik library not found. Building without Sputnik support.")
    message(STATUS "To use Sputnik, please:")
    message(STATUS "  1. Clone Sputnik: git clone https://github.com/google-research/sputnik.git")
    message(STATUS "  2. Build and install Sputnik following their instructions")
    message(STATUS "  3. Set SPUTNIK_INCLUDE_DIR and SPUTNIK_LIBRARY variables")
    
    # Add a preprocessor definition to conditionally compile Sputnik code
    target_compile_definitions(spmm PRIVATE NO_SPUTNIK)
endif()

# Compiler-specific options
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(spmm PRIVATE 
        $<$<COMPILE_LANGUAGE:CUDA>:-G -g>
        $<$<COMPILE_LANGUAGE:CXX>:-g>
    )
else()
    target_compile_options(spmm PRIVATE 
        $<$<COMPILE_LANGUAGE:CUDA>:-O3>
        $<$<COMPILE_LANGUAGE:CXX>:-O3>
    )
endif()

# Add custom target to create matrix directory
add_custom_target(create_matrix_dir ALL
    COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_CURRENT_BINARY_DIR}/matrix
    COMMENT "Creating matrix directory for test files"
)

# Make spmm depend on matrix directory creation
add_dependencies(spmm create_matrix_dir)

# Print build information
message(STATUS "CUDA Toolkit Version: ${CUDAToolkit_VERSION}")
message(STATUS "CUDA Architectures: ${CMAKE_CUDA_ARCHITECTURES}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")

# Optional: Add install target
install(TARGETS spmm
    RUNTIME DESTINATION bin
)

# Optional: Add a test target (if you have test matrices)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/matrix)
    add_custom_target(copy_test_matrices ALL
        COMMAND ${CMAKE_COMMAND} -E copy_directory 
                ${CMAKE_CURRENT_SOURCE_DIR}/matrix 
                ${CMAKE_CURRENT_BINARY_DIR}/matrix
        COMMENT "Copying test matrices to build directory"
    )
    add_dependencies(spmm copy_test_matrices)
endif()
