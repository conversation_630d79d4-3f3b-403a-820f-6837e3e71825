add_executable(sigmoid sigmoid.cu)
target_link_libraries(sigmoid PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sigmoid PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(sigmoid PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(sigmoid_v0 sigmoid_v0.cu)
target_link_libraries(sigmoid_v0 PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sigmoid_v0 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(sigmoid_v0 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(sigmoid_v1 sigmoid_v1.cu)
target_link_libraries(sigmoid_v1 PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sigmoid_v1 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(sigmoid_v1 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)