#include <algorithm>
#include <cuda_bf16.h>
#include <cuda_fp16.h>
#include <cuda_fp8.h>
#include <cuda_runtime.h>
#include <float.h>
#include <stdio.h>
#include <stdlib.h>
#include <vector>
#include <cstdint>

#define WARP_SIZE 32
#define INT4(value) (reinterpret_cast<int4 *>(&(value))[0])
#define FLOAT4(value) (reinterpret_cast<float4 *>(&(value))[0])
#define HALF2(value) (reinterpret_cast<half2 *>(&(value))[0])
#define BFLOAT2(value) (reinterpret_cast<__nv_bfloat162 *>(&(value))[0])
#define LDST128BITS(value) (reinterpret_cast<float4 *>(&(value))[0])
#define MAX_EXP_F32 88.3762626647949f
#define MIN_EXP_F32 -88.3762626647949f
#define MAX_EXP_F16 __float2half(11.089866488461016f)
#define MIN_EXP_F16 __float2half(-9.704060527839234f)

// FP32
// Sigmoid x: N, y: N y=1/(1+exp(-x))
// grid(N/256), block(K=256)
__global__ void sigmoid_f32_kernel(float *x, float *y, int N) {
  int idx = blockIdx.x * blockDim.x + threadIdx.x;
  if (idx < N) {
    float v = x[idx];
    v = fminf(fmaxf(v, MIN_EXP_F32), MAX_EXP_F32);
    y[idx] = 1.0f / (1.0f + expf(-v));
  }
}

// Sigmoid x: N, y: N y=1/(1+exp(-x)) Vec4
// grid(N/256), block(256/4)
__global__ void sigmoid_f32x4_kernel(float *x, float *y, int N) {
  int idx = (blockIdx.x * blockDim.x + threadIdx.x) * 4;
  float4 reg_x = FLOAT4(x[idx]);
  float4 reg_y;

  reg_x.x = fminf(fmaxf(reg_x.x, MIN_EXP_F32), MAX_EXP_F32);
  reg_x.y = fminf(fmaxf(reg_x.y, MIN_EXP_F32), MAX_EXP_F32);
  reg_x.z = fminf(fmaxf(reg_x.z, MIN_EXP_F32), MAX_EXP_F32);
  reg_x.w = fminf(fmaxf(reg_x.w, MIN_EXP_F32), MAX_EXP_F32);

  reg_y.x = 1.0f / (1.0f + expf(-reg_x.x));
  reg_y.y = 1.0f / (1.0f + expf(-reg_x.y));
  reg_y.z = 1.0f / (1.0f + expf(-reg_x.z));
  reg_y.w = 1.0f / (1.0f + expf(-reg_x.w));

  if ((idx + 0) < N) {
    FLOAT4(y[idx]) = reg_y;
  }
}

//  FP16
__global__ void sigmoid_f16_kernel(half *x, half *y, int N) {
  int idx = blockIdx.x * blockDim.x + threadIdx.x;
  const half f = __float2half(1.0f);
  if (idx < N) {
    half v = x[idx];
    v = __hmin(__hmax(v, MIN_EXP_F16), MAX_EXP_F16);
    y[idx] = f / (f + hexp(-v));
  }
}

__global__ void sigmoid_f16x2_kernel(half *x, half *y, int N) {
  int idx = (blockIdx.x * blockDim.x + threadIdx.x) * 2;
  const half f = __float2half(1.0f);
  half2 reg_x = HALF2(x[idx]);
  half2 reg_y;
  reg_x.x = __hmin(__hmax(reg_x.x, MIN_EXP_F16), MAX_EXP_F16);
  reg_x.y = __hmin(__hmax(reg_x.y, MIN_EXP_F16), MAX_EXP_F16);

  reg_y.x = f / (f + hexp(-reg_x.x));
  reg_y.y = f / (f + hexp(-reg_x.y));

  if ((idx + 0) < N) {
    HALF2(y[idx]) = reg_y;
  }
}

// unpack f16x8
__global__ void sigmoid_f16x8_kernel(half *x, half *y, int N) {
  int idx = (blockIdx.x * blockDim.x + threadIdx.x) * 8;
  const half f = __float2half(1.0f);

  half2 reg_x_0 = HALF2(x[idx + 0]);
  half2 reg_x_1 = HALF2(x[idx + 2]);
  half2 reg_x_2 = HALF2(x[idx + 4]);
  half2 reg_x_3 = HALF2(x[idx + 6]);

  reg_x_0.x = __hmin(__hmax(reg_x_0.x, MIN_EXP_F16), MAX_EXP_F16);
  reg_x_0.y = __hmin(__hmax(reg_x_0.y, MIN_EXP_F16), MAX_EXP_F16);
  reg_x_1.x = __hmin(__hmax(reg_x_1.x, MIN_EXP_F16), MAX_EXP_F16);
  reg_x_1.y = __hmin(__hmax(reg_x_1.y, MIN_EXP_F16), MAX_EXP_F16);
  reg_x_2.x = __hmin(__hmax(reg_x_2.x, MIN_EXP_F16), MAX_EXP_F16);
  reg_x_2.y = __hmin(__hmax(reg_x_2.y, MIN_EXP_F16), MAX_EXP_F16);
  reg_x_3.x = __hmin(__hmax(reg_x_3.x, MIN_EXP_F16), MAX_EXP_F16);
  reg_x_3.y = __hmin(__hmax(reg_x_3.y, MIN_EXP_F16), MAX_EXP_F16);

  half2 reg_y_0, reg_y_1, reg_y_2, reg_y_3;

  reg_y_0.x = f / (f + hexp(-reg_x_0.x));
  reg_y_0.y = f / (f + hexp(-reg_x_0.y));
  reg_y_1.x = f / (f + hexp(-reg_x_1.x));
  reg_y_1.y = f / (f + hexp(-reg_x_1.y));
  reg_y_2.x = f / (f + hexp(-reg_x_2.x));
  reg_y_2.y = f / (f + hexp(-reg_x_2.y));
  reg_y_3.x = f / (f + hexp(-reg_x_3.x));
  reg_y_3.y = f / (f + hexp(-reg_x_3.y));

  if ((idx + 0) < N) {
    HALF2(y[idx + 0]) = reg_y_0;
  }
  if ((idx + 2) < N) {
    HALF2(y[idx + 2]) = reg_y_1;
  }
  if ((idx + 4) < N) {
    HALF2(y[idx + 4]) = reg_y_2;
  }
  if ((idx + 6) < N) {
    HALF2(y[idx + 6]) = reg_y_3;
  }
}

// pack f16x8 - Safe version without 128-bit access
__global__ void sigmoid_f16x8_pack_kernel(half *x, half *y, int N) {
  int idx = (blockIdx.x * blockDim.x + threadIdx.x) * 8;
  const half f = __float2half(1.0f);

  // Process 8 elements per thread using individual access
#pragma unroll
  for (int i = 0; i < 8; ++i) {
    int global_idx = idx + i;
    if (global_idx < N) {
      half v = __hmin(__hmax(x[global_idx], MIN_EXP_F16), MAX_EXP_F16);
      y[global_idx] = f / (f + hexp(-v));
    }
  }
}

// Helper function to check CUDA errors
#define CHECK_CUDA(call) \
  do { \
    cudaError_t error = call; \
    if (error != cudaSuccess) { \
      printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(error)); \
      exit(1); \
    } \
  } while(0)

// CPU reference implementation for verification
void sigmoid_cpu_reference(const std::vector<float>& input, std::vector<float>& output) {
  for (size_t i = 0; i < input.size(); i++) {
    float x = input[i];
    // Apply same clamping as GPU version
    x = std::min(std::max(x, MIN_EXP_F32), MAX_EXP_F32);
    output[i] = 1.0f / (1.0f + expf(-x));
  }
}

// Test function for FP32 kernels
void test_f32_kernels(int N) {
  printf("Testing FP32 sigmoid kernels with N=%d\n", N);

  // Host memory allocation
  std::vector<float> h_x(N), h_y(N), h_ref(N);

  // Initialize input data with various ranges
  for (int i = 0; i < N; i++) {
    // Generate values in range [-10, 10] to test sigmoid behavior
    h_x[i] = (static_cast<float>(i % 2000) - 1000.0f) / 100.0f;
  }

  // Compute reference result on CPU
  sigmoid_cpu_reference(h_x, h_ref);

  // Device memory allocation
  float *d_x, *d_y;
  CHECK_CUDA(cudaMalloc(&d_x, N * sizeof(float)));
  CHECK_CUDA(cudaMalloc(&d_y, N * sizeof(float)));

  // Copy input data to device
  CHECK_CUDA(cudaMemcpy(d_x, h_x.data(), N * sizeof(float), cudaMemcpyHostToDevice));

  // Test basic FP32 kernel
  dim3 block(256);
  dim3 grid((N + block.x - 1) / block.x);

  cudaEvent_t start, stop;
  CHECK_CUDA(cudaEventCreate(&start));
  CHECK_CUDA(cudaEventCreate(&stop));

  CHECK_CUDA(cudaEventRecord(start));
  sigmoid_f32_kernel<<<grid, block>>>(d_x, d_y, N);
  CHECK_CUDA(cudaEventRecord(stop));

  CHECK_CUDA(cudaGetLastError());
  CHECK_CUDA(cudaDeviceSynchronize());

  float milliseconds = 0;
  CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));

  // Copy result back and verify
  CHECK_CUDA(cudaMemcpy(h_y.data(), d_y, N * sizeof(float), cudaMemcpyDeviceToHost));

  bool correct = true;
  float max_error = 0.0f;
  for (int i = 0; i < N; i++) {
    float error = fabsf(h_y[i] - h_ref[i]);
    max_error = fmaxf(max_error, error);
    if (error > 1e-5) {
      correct = false;
      if (i < 10) { // Print first few errors
        printf("Error at %d: got %.6f, expected %.6f, diff %.6f\n",
               i, h_y[i], h_ref[i], error);
      }
    }
  }
  printf("FP32 basic kernel: %s (%.3f ms, max_error=%.2e)\n",
         correct ? "PASSED" : "FAILED", milliseconds, max_error);

  // Test FP32x4 vectorized kernel
  if (N % 4 == 0) {
    dim3 block_vec(64); // 256/4 = 64
    dim3 grid_vec((N/4 + block_vec.x - 1) / block_vec.x);

    CHECK_CUDA(cudaEventRecord(start));
    sigmoid_f32x4_kernel<<<grid_vec, block_vec>>>(d_x, d_y, N);
    CHECK_CUDA(cudaEventRecord(stop));

    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    CHECK_CUDA(cudaMemcpy(h_y.data(), d_y, N * sizeof(float), cudaMemcpyDeviceToHost));

    correct = true;
    max_error = 0.0f;
    for (int i = 0; i < N; i++) {
      float error = fabsf(h_y[i] - h_ref[i]);
      max_error = fmaxf(max_error, error);
      if (error > 1e-5) {
        correct = false;
        break;
      }
    }
    printf("FP32x4 vectorized kernel: %s (%.3f ms, max_error=%.2e)\n",
           correct ? "PASSED" : "FAILED", milliseconds, max_error);
  }

  // Cleanup
  CHECK_CUDA(cudaFree(d_x));
  CHECK_CUDA(cudaFree(d_y));
  CHECK_CUDA(cudaEventDestroy(start));
  CHECK_CUDA(cudaEventDestroy(stop));
}

// Test function for FP16 kernels
void test_f16_kernels(int N) {
  printf("Testing FP16 sigmoid kernels with N=%d\n", N);

  // Host memory allocation
  std::vector<half> h_x(N), h_y(N);
  std::vector<float> h_x_float(N), h_ref(N);

  // Initialize input data
  for (int i = 0; i < N; i++) {
    float val = (static_cast<float>(i % 2000) - 1000.0f) / 100.0f;
    h_x_float[i] = val;
    h_x[i] = __float2half(val);
  }

  // Compute reference result on CPU
  sigmoid_cpu_reference(h_x_float, h_ref);

  // Device memory allocation
  half *d_x, *d_y;
  CHECK_CUDA(cudaMalloc(&d_x, N * sizeof(half)));
  CHECK_CUDA(cudaMalloc(&d_y, N * sizeof(half)));

  // Copy input data to device
  CHECK_CUDA(cudaMemcpy(d_x, h_x.data(), N * sizeof(half), cudaMemcpyHostToDevice));

  cudaEvent_t start, stop;
  CHECK_CUDA(cudaEventCreate(&start));
  CHECK_CUDA(cudaEventCreate(&stop));

  // Test basic FP16 kernel
  dim3 block(256);
  dim3 grid((N + block.x - 1) / block.x);

  float milliseconds = 0;
  CHECK_CUDA(cudaEventRecord(start));
  sigmoid_f16_kernel<<<grid, block>>>(d_x, d_y, N);
  CHECK_CUDA(cudaEventRecord(stop));

  CHECK_CUDA(cudaGetLastError());
  CHECK_CUDA(cudaDeviceSynchronize());
  CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));

  // Copy result back and verify
  CHECK_CUDA(cudaMemcpy(h_y.data(), d_y, N * sizeof(half), cudaMemcpyDeviceToHost));

  bool correct = true;
  float max_error = 0.0f;
  for (int i = 0; i < N; i++) {
    float result = __half2float(h_y[i]);
    float error = fabsf(result - h_ref[i]);
    max_error = fmaxf(max_error, error);
    if (error > 1e-2) { // Looser tolerance for FP16
      correct = false;
      break;
    }
  }
  printf("FP16 basic kernel: %s (%.3f ms, max_error=%.2e)\n",
         correct ? "PASSED" : "FAILED", milliseconds, max_error);

  // Test FP16x2 kernel
  if (N % 2 == 0) {
    dim3 block_vec(128); // 256/2 = 128
    dim3 grid_vec((N/2 + block_vec.x - 1) / block_vec.x);

    CHECK_CUDA(cudaEventRecord(start));
    sigmoid_f16x2_kernel<<<grid_vec, block_vec>>>(d_x, d_y, N);
    CHECK_CUDA(cudaEventRecord(stop));

    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());
    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    CHECK_CUDA(cudaMemcpy(h_y.data(), d_y, N * sizeof(half), cudaMemcpyDeviceToHost));

    correct = true;
    max_error = 0.0f;
    for (int i = 0; i < N; i++) {
      float result = __half2float(h_y[i]);
      float error = fabsf(result - h_ref[i]);
      max_error = fmaxf(max_error, error);
      if (error > 1e-2) {
        correct = false;
        break;
      }
    }
    printf("FP16x2 vectorized kernel: %s (%.3f ms, max_error=%.2e)\n",
           correct ? "PASSED" : "FAILED", milliseconds, max_error);
  }

  // Test FP16x8 kernel
  if (N % 8 == 0) {
    dim3 block_vec(32); // 256/8 = 32
    dim3 grid_vec((N/8 + block_vec.x - 1) / block_vec.x);

    CHECK_CUDA(cudaEventRecord(start));
    sigmoid_f16x8_kernel<<<grid_vec, block_vec>>>(d_x, d_y, N);
    CHECK_CUDA(cudaEventRecord(stop));

    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());
    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    CHECK_CUDA(cudaMemcpy(h_y.data(), d_y, N * sizeof(half), cudaMemcpyDeviceToHost));

    correct = true;
    max_error = 0.0f;
    for (int i = 0; i < N; i++) {
      float result = __half2float(h_y[i]);
      float error = fabsf(result - h_ref[i]);
      max_error = fmaxf(max_error, error);
      if (error > 1e-2) {
        correct = false;
        break;
      }
    }
    printf("FP16x8 unpack kernel: %s (%.3f ms, max_error=%.2e)\n",
           correct ? "PASSED" : "FAILED", milliseconds, max_error);

    // Test FP16x8 pack kernel
    CHECK_CUDA(cudaEventRecord(start));
    sigmoid_f16x8_pack_kernel<<<grid_vec, block_vec>>>(d_x, d_y, N);
    CHECK_CUDA(cudaEventRecord(stop));

    CHECK_CUDA(cudaGetLastError());
    CHECK_CUDA(cudaDeviceSynchronize());
    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    CHECK_CUDA(cudaMemcpy(h_y.data(), d_y, N * sizeof(half), cudaMemcpyDeviceToHost));

    correct = true;
    max_error = 0.0f;
    for (int i = 0; i < N; i++) {
      float result = __half2float(h_y[i]);
      float error = fabsf(result - h_ref[i]);
      max_error = fmaxf(max_error, error);
      if (error > 1e-2) {
        correct = false;
        break;
      }
    }
    printf("FP16x8 pack kernel: %s (%.3f ms, max_error=%.2e)\n",
           correct ? "PASSED" : "FAILED", milliseconds, max_error);
  }

  // Cleanup
  CHECK_CUDA(cudaFree(d_x));
  CHECK_CUDA(cudaFree(d_y));
  CHECK_CUDA(cudaEventDestroy(start));
  CHECK_CUDA(cudaEventDestroy(stop));
}

int main(int argc, char* argv[]) {
  printf("CUDA Sigmoid Activation Function Test\n");
  printf("====================================\n");

  // Check CUDA device
  int deviceCount;
  CHECK_CUDA(cudaGetDeviceCount(&deviceCount));
  if (deviceCount == 0) {
    printf("No CUDA devices found!\n");
    return 1;
  }

  cudaDeviceProp prop;
  CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
  printf("Using device: %s\n", prop.name);
  printf("Compute capability: %d.%d\n", prop.major, prop.minor);
  printf("\n");

  // Parse command line arguments
  std::vector<int> test_sizes;

  if (argc > 1) {
    // Use command line arguments as test sizes
    printf("Using custom test sizes from command line:\n");
    for (int i = 1; i < argc; i++) {
      int size = atoi(argv[i]);
      if (size > 0) {
        test_sizes.push_back(size);
        printf("  - %d\n", size);
      } else {
        printf("Warning: Invalid size '%s' ignored\n", argv[i]);
      }
    }
    printf("\n");
  } else {
    // Use default test sizes
    test_sizes = {1024, 4096, 16384, 65536};
    printf("Using default test sizes: 1024, 4096, 16384, 65536\n");
    printf("Usage: %s [size1] [size2] ... to specify custom sizes\n\n", argv[0]);
  }

  if (test_sizes.empty()) {
    printf("No valid test sizes provided!\n");
    return 1;
  }

  // Test sigmoid function behavior with sample values
  printf("Sigmoid function behavior test:\n");
  printf("Input -> Output\n");
  std::vector<float> test_values = {-10.0f, -5.0f, -2.0f, -1.0f, 0.0f, 1.0f, 2.0f, 5.0f, 10.0f};
  for (float x : test_values) {
    float clamped_x = std::min(std::max(x, MIN_EXP_F32), MAX_EXP_F32);
    float y = 1.0f / (1.0f + expf(-clamped_x));
    printf("%6.1f -> %.6f\n", x, y);
  }
  printf("\n");

  // Run tests for different sizes
  for (int N : test_sizes) {
    printf("========================================\n");
    printf("Testing with N = %d elements\n", N);
    printf("========================================\n");

    test_f32_kernels(N);
    printf("\n");

    test_f16_kernels(N);
    printf("\n");
  }

  // Performance comparison
  printf("Performance Summary (last test size):\n");
  printf("====================================\n");

  int N = test_sizes.back();
  const int num_runs = 100;

  // FP32 performance test
  {
    std::vector<float> h_x(N);
    for (int i = 0; i < N; i++) {
      h_x[i] = (static_cast<float>(i % 2000) - 1000.0f) / 100.0f;
    }

    float *d_x, *d_y;
    CHECK_CUDA(cudaMalloc(&d_x, N * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_y, N * sizeof(float)));
    CHECK_CUDA(cudaMemcpy(d_x, h_x.data(), N * sizeof(float), cudaMemcpyHostToDevice));

    cudaEvent_t start, stop;
    CHECK_CUDA(cudaEventCreate(&start));
    CHECK_CUDA(cudaEventCreate(&stop));

    // Basic FP32 kernel
    dim3 block(256);
    dim3 grid((N + block.x - 1) / block.x);

    float total_time = 0.0f;
    for (int run = 0; run < num_runs; run++) {
      CHECK_CUDA(cudaEventRecord(start));
      sigmoid_f32_kernel<<<grid, block>>>(d_x, d_y, N);
      CHECK_CUDA(cudaEventRecord(stop));
      CHECK_CUDA(cudaDeviceSynchronize());

      float milliseconds;
      CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
      total_time += milliseconds;
    }
    printf("FP32 basic kernel avg time: %.3f ms\n", total_time / num_runs);

    // Vectorized FP32 kernel
    if (N % 4 == 0) {
      dim3 block_vec(64);
      dim3 grid_vec((N/4 + block_vec.x - 1) / block_vec.x);

      total_time = 0.0f;
      for (int run = 0; run < num_runs; run++) {
        CHECK_CUDA(cudaEventRecord(start));
        sigmoid_f32x4_kernel<<<grid_vec, block_vec>>>(d_x, d_y, N);
        CHECK_CUDA(cudaEventRecord(stop));
        CHECK_CUDA(cudaDeviceSynchronize());

        float milliseconds;
        CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
        total_time += milliseconds;
      }
      printf("FP32x4 vectorized kernel avg time: %.3f ms\n", total_time / num_runs);
    }

    CHECK_CUDA(cudaFree(d_x));
    CHECK_CUDA(cudaFree(d_y));
    CHECK_CUDA(cudaEventDestroy(start));
    CHECK_CUDA(cudaEventDestroy(stop));
  }

  printf("\nAll tests completed!\n");
  return 0;
}