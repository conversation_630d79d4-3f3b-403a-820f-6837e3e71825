#include <algorithm>
#include <cuda_runtime.h>
#include <stdio.h>
#include <stdlib.h>
#include <vector>
#include <chrono>
#include <cmath>
#include <iostream>

#define WARP_SIZE 32
#define FLOAT4(value) (reinterpret_cast<float4*>(&(value))[0])

#define checkCudaErrors(func)                                                  \
  {                                                                            \
    cudaError_t e = (func);                                                    \
    if (e != cudaSuccess)                                                      \
      printf("%s %d CUDA: %s\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
  }

// Warp reduce sum function
template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ float warp_reduce_sum(float val) {
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val += __shfl_xor_sync(0xffffffff, val, mask);
  }
  return val;
}

// Block reduce sum function
template <const int NUM_THREADS>
__device__ __forceinline__ float block_reduce_sum(float val) {
  constexpr int NUM_WARPS = (NUM_THREADS + WARP_SIZE - 1) / WARP_SIZE;
  __shared__ float reduce_smem[NUM_WARPS];

  int warp = threadIdx.x / WARP_SIZE;
  int lane = threadIdx.x % WARP_SIZE;

  val = warp_reduce_sum<WARP_SIZE>(val);
  if (lane == 0) reduce_smem[warp] = val;
  __syncthreads();

  val = (lane < NUM_WARPS) ? reduce_smem[lane] : 0.0f;
  val = warp_reduce_sum<NUM_WARPS>(val);

  return val;
}

// Two-stage vectorized softmax implementation for multi-block support

// Stage 1: Compute exp values and accumulate global sum (Vec4)
template<const int NUM_THREADS = 128/4>
__global__ void softmax_v2_stage1(float* x, float* exp_vals, float* total, int N) {
  const int tid = threadIdx.x;
  const int idx = (blockIdx.x * blockDim.x + tid) * 4;

  // Safe vectorized memory access with boundary checking
  float4 reg_x;
  if (idx + 3 < N) {
    // Safe to read 4 elements at once
    reg_x = FLOAT4(x[idx]);
  } else {
    // Boundary case: read individual elements safely
    reg_x.x = (idx + 0 < N) ? x[idx + 0] : -1000.0f;
    reg_x.y = (idx + 1 < N) ? x[idx + 1] : -1000.0f;
    reg_x.z = (idx + 2 < N) ? x[idx + 2] : -1000.0f;
    reg_x.w = (idx + 3 < N) ? x[idx + 3] : -1000.0f;
  }

  // Clamp values to prevent overflow
  reg_x.x = fminf(fmaxf(reg_x.x, -50.0f), 50.0f);
  reg_x.y = fminf(fmaxf(reg_x.y, -50.0f), 50.0f);
  reg_x.z = fminf(fmaxf(reg_x.z, -50.0f), 50.0f);
  reg_x.w = fminf(fmaxf(reg_x.w, -50.0f), 50.0f);

  float4 reg_exp;
  reg_exp.x = expf(reg_x.x);
  reg_exp.y = expf(reg_x.y);
  reg_exp.z = expf(reg_x.z);
  reg_exp.w = expf(reg_x.w);

  // Store exp values for stage 2
  if (idx + 3 < N) {
    FLOAT4(exp_vals[idx]) = reg_exp;
  } else {
    if (idx + 0 < N) exp_vals[idx + 0] = reg_exp.x;
    if (idx + 1 < N) exp_vals[idx + 1] = reg_exp.y;
    if (idx + 2 < N) exp_vals[idx + 2] = reg_exp.z;
    if (idx + 3 < N) exp_vals[idx + 3] = reg_exp.w;
  }

  // Sum for reduction
  float exp_val = 0.0f;
  if (idx < N) {
    exp_val += (idx + 0 < N) ? reg_exp.x : 0.0f;
    exp_val += (idx + 1 < N) ? reg_exp.y : 0.0f;
    exp_val += (idx + 2 < N) ? reg_exp.z : 0.0f;
    exp_val += (idx + 3 < N) ? reg_exp.w : 0.0f;
  }

  float sum = block_reduce_sum<NUM_THREADS>(exp_val);

  // Accumulate to global total
  if (tid == 0) {
    atomicAdd(total, sum);
  }
}

// Stage 2: Normalize using the computed global sum (Vec4)
template<const int NUM_THREADS = 128/4>
__global__ void softmax_v2_stage2(float* exp_vals, float* y, float total_sum, int N) {
  const int tid = threadIdx.x;
  const int idx = (blockIdx.x * blockDim.x + tid) * 4;

  if (idx < N && total_sum > 0.0f) {
    // Safe vectorized memory access
    float4 reg_exp;
    if (idx + 3 < N) {
      reg_exp = FLOAT4(exp_vals[idx]);
    } else {
      reg_exp.x = (idx + 0 < N) ? exp_vals[idx + 0] : 0.0f;
      reg_exp.y = (idx + 1 < N) ? exp_vals[idx + 1] : 0.0f;
      reg_exp.z = (idx + 2 < N) ? exp_vals[idx + 2] : 0.0f;
      reg_exp.w = (idx + 3 < N) ? exp_vals[idx + 3] : 0.0f;
    }

    float4 reg_y;
    reg_y.x = reg_exp.x / total_sum;
    reg_y.y = reg_exp.y / total_sum;
    reg_y.z = reg_exp.z / total_sum;
    reg_y.w = reg_exp.w / total_sum;

    // Safe vectorized write
    if (idx + 3 < N) {
      FLOAT4(y[idx]) = reg_y;
    } else {
      if (idx + 0 < N) y[idx + 0] = reg_y.x;
      if (idx + 1 < N) y[idx + 1] = reg_y.y;
      if (idx + 2 < N) y[idx + 2] = reg_y.z;
      if (idx + 3 < N) y[idx + 3] = reg_y.w;
    }
  } else if (idx < N) {
    if (idx + 0 < N) y[idx + 0] = 0.0f;
    if (idx + 1 < N) y[idx + 1] = 0.0f;
    if (idx + 2 < N) y[idx + 2] = 0.0f;
    if (idx + 3 < N) y[idx + 3] = 0.0f;
  }
}

// Multi-block single-kernel version with __threadfence() synchronization (Vec4)
template<const int NUM_THREADS = 128/4>
__global__ void softmax_v2_multiblock(float* x, float* y, float* total, int N) {
  const int tid = threadIdx.x;
  const int idx = (blockIdx.x * blockDim.x + tid) * 4;

  // Safe vectorized memory access with boundary checking
  float4 reg_x;
  if (idx + 3 < N) {
    // Safe to read 4 elements at once
    reg_x = FLOAT4(x[idx]);
  } else {
    // Boundary case: read individual elements safely
    reg_x.x = (idx + 0 < N) ? x[idx + 0] : -1000.0f;
    reg_x.y = (idx + 1 < N) ? x[idx + 1] : -1000.0f;
    reg_x.z = (idx + 2 < N) ? x[idx + 2] : -1000.0f;
    reg_x.w = (idx + 3 < N) ? x[idx + 3] : -1000.0f;
  }

  // Clamp values to prevent overflow
  reg_x.x = fminf(fmaxf(reg_x.x, -50.0f), 50.0f);
  reg_x.y = fminf(fmaxf(reg_x.y, -50.0f), 50.0f);
  reg_x.z = fminf(fmaxf(reg_x.z, -50.0f), 50.0f);
  reg_x.w = fminf(fmaxf(reg_x.w, -50.0f), 50.0f);

  float4 reg_exp;
  reg_exp.x = expf(reg_x.x);
  reg_exp.y = expf(reg_x.y);
  reg_exp.z = expf(reg_x.z);
  reg_exp.w = expf(reg_x.w);

  // Sum for reduction
  float exp_val = 0.0f;
  if (idx < N) {
    exp_val += (idx + 0 < N) ? reg_exp.x : 0.0f;
    exp_val += (idx + 1 < N) ? reg_exp.y : 0.0f;
    exp_val += (idx + 2 < N) ? reg_exp.z : 0.0f;
    exp_val += (idx + 3 < N) ? reg_exp.w : 0.0f;
  }

  float sum = block_reduce_sum<NUM_THREADS>(exp_val);

  // Each block contributes to global sum
  if (tid == 0) {
    atomicAdd(total, sum);
  }

  // Grid-wide memory fence to ensure all atomic operations are visible
  __threadfence();

  // Now normalize using the global total
  if (idx < N) {
    float global_total = *total;
    if (global_total > 0.0f) {
      float4 reg_y;
      reg_y.x = (idx + 0 < N) ? reg_exp.x / global_total : 0.0f;
      reg_y.y = (idx + 1 < N) ? reg_exp.y / global_total : 0.0f;
      reg_y.z = (idx + 2 < N) ? reg_exp.z / global_total : 0.0f;
      reg_y.w = (idx + 3 < N) ? reg_exp.w / global_total : 0.0f;

      // Safe vectorized write
      if (idx + 3 < N) {
        FLOAT4(y[idx]) = reg_y;
      } else {
        if (idx + 0 < N) y[idx + 0] = reg_y.x;
        if (idx + 1 < N) y[idx + 1] = reg_y.y;
        if (idx + 2 < N) y[idx + 2] = reg_y.z;
        if (idx + 3 < N) y[idx + 3] = reg_y.w;
      }
    } else {
      if (idx + 0 < N) y[idx + 0] = 0.0f;
      if (idx + 1 < N) y[idx + 1] = 0.0f;
      if (idx + 2 < N) y[idx + 2] = 0.0f;
      if (idx + 3 < N) y[idx + 3] = 0.0f;
    }
  }
}

// Legacy single-kernel version (works correctly only for single block)
template<const int NUM_THREADS = 128/4>
__global__ void softmax_v2_vec4(float* x, float* y, float* total, int N) {
  const int tid = threadIdx.x;
  const int idx = (blockIdx.x * blockDim.x + tid) * 4;

  __shared__ float shared_sum;
  if (tid == 0) shared_sum = 0.0f;
  __syncthreads();

  float4 reg_x = (idx < N) ? FLOAT4(x[idx]) : make_float4(-1000.0f, -1000.0f, -1000.0f, -1000.0f);

  // Clamp values to prevent overflow
  reg_x.x = fminf(fmaxf(reg_x.x, -50.0f), 50.0f);
  reg_x.y = fminf(fmaxf(reg_x.y, -50.0f), 50.0f);
  reg_x.z = fminf(fmaxf(reg_x.z, -50.0f), 50.0f);
  reg_x.w = fminf(fmaxf(reg_x.w, -50.0f), 50.0f);

  float4 reg_exp;
  reg_exp.x = expf(reg_x.x);
  reg_exp.y = expf(reg_x.y);
  reg_exp.z = expf(reg_x.z);
  reg_exp.w = expf(reg_x.w);

  float exp_val = (reg_exp.x + reg_exp.y + reg_exp.z + reg_exp.w);
  float sum = block_reduce_sum<NUM_THREADS>(exp_val);

  if (tid == 0) {
    shared_sum = sum;
    atomicAdd(total, sum);
  }
  __syncthreads();

  // Normalize (only correct for single block!)
  if (idx < N && shared_sum > 0.0f) {
    float4 reg_y;
    reg_y.x = reg_exp.x / shared_sum;
    reg_y.y = reg_exp.y / shared_sum;
    reg_y.z = reg_exp.z / shared_sum;
    reg_y.w = reg_exp.w / shared_sum;

    // Handle boundary cases for the last elements
    if (idx + 3 < N) {
      FLOAT4(y[idx]) = reg_y;
    } else {
      if (idx + 0 < N) y[idx + 0] = reg_y.x;
      if (idx + 1 < N) y[idx + 1] = reg_y.y;
      if (idx + 2 < N) y[idx + 2] = reg_y.z;
      if (idx + 3 < N) y[idx + 3] = reg_y.w;
    }
  } else if (idx < N) {
    if (idx + 0 < N) y[idx + 0] = 0.0f;
    if (idx + 1 < N) y[idx + 1] = 0.0f;
    if (idx + 2 < N) y[idx + 2] = 0.0f;
    if (idx + 3 < N) y[idx + 3] = 0.0f;
  }
}

// CPU reference implementation
void softmax_cpu(const float* x, float* y, int N) {
  // Find max for numerical stability
  float max_val = x[0];
  for (int i = 1; i < N; i++) {
    max_val = std::max(max_val, x[i]);
  }

  // Compute exp(x - max) and sum
  float sum = 0.0f;
  for (int i = 0; i < N; i++) {
    y[i] = expf(x[i] - max_val);
    sum += y[i];
  }

  // Normalize
  for (int i = 0; i < N; i++) {
    y[i] /= sum;
  }
}

// Initialize vector with test data
void init_vector(float* x, int N) {
  for (int i = 0; i < N; i++) {
    x[i] = static_cast<float>(rand()) / RAND_MAX * 10.0f - 5.0f; // Range [-5, 5]
  }
}

// Verify results
bool verify_result(const float* gpu_result, const float* cpu_result, int N, float tolerance = 0.1f) {
  int failed_count = 0;
  float max_diff = 0.0f;

  for (int i = 0; i < N; i++) {
    float diff = std::abs(gpu_result[i] - cpu_result[i]);
    max_diff = std::max(max_diff, diff);

    if (diff > tolerance) {
      if (failed_count < 5) { // Only print first 5 failures
        printf("Verification failed at index %d: GPU=%.6f, CPU=%.6f, diff=%.6e\n",
               i, gpu_result[i], cpu_result[i], diff);
      }
      failed_count++;
    }
  }

  printf("Max difference: %.6e, Failed elements: %d/%d\n", max_diff, failed_count, N);
  return failed_count == 0;
}

// Performance testing function
template<typename KernelFunc>
double benchmark_kernel(KernelFunc kernel_func, float* d_x, float* d_y, float* d_total,
                       int N, int num_iterations = 1000) {
  // Warm up
  for (int i = 0; i < 10; i++) {
    checkCudaErrors(cudaMemset(d_total, 0, sizeof(float)));
    kernel_func(d_x, d_y, d_total, N);
  }
  checkCudaErrors(cudaDeviceSynchronize());

  // Timing
  auto start = std::chrono::high_resolution_clock::now();
  for (int i = 0; i < num_iterations; i++) {
    checkCudaErrors(cudaMemset(d_total, 0, sizeof(float)));
    kernel_func(d_x, d_y, d_total, N);
  }
  checkCudaErrors(cudaDeviceSynchronize());
  auto end = std::chrono::high_resolution_clock::now();

  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
  return duration.count() / 1000.0 / num_iterations; // ms per iteration
}

int main(int argc, char **argv) {
  // Parse command line arguments
  int N = 1024;
  if (argc >= 2) {
    N = atoi(argv[1]);
  }

  // Ensure N is aligned for float4 operations
  N = (N + 3) / 4 * 4;

  printf("Softmax v2 (Vec4) Performance Test\n");
  printf("Vector size: %d\n", N);
  printf("=================================\n");

  // Allocate host memory
  size_t size = N * sizeof(float);
  float *h_x = (float*)malloc(size);
  float *h_y_gpu = (float*)malloc(size);
  float *h_y_cpu = (float*)malloc(size);

  // Initialize input vector
  srand(42); // For reproducible results
  init_vector(h_x, N);

  // Allocate device memory
  float *d_x, *d_y, *d_total;
  checkCudaErrors(cudaMalloc(&d_x, size));
  checkCudaErrors(cudaMalloc(&d_y, size));
  checkCudaErrors(cudaMalloc(&d_total, sizeof(float)));

  // Copy input data to device
  checkCudaErrors(cudaMemcpy(d_x, h_x, size, cudaMemcpyHostToDevice));

  // Compute CPU reference
  printf("Computing CPU reference...\n");
  auto cpu_start = std::chrono::high_resolution_clock::now();
  softmax_cpu(h_x, h_y_cpu, N);
  auto cpu_end = std::chrono::high_resolution_clock::now();
  auto cpu_time = std::chrono::duration_cast<std::chrono::microseconds>(cpu_end - cpu_start);
  printf("CPU time: %.3f ms\n\n", cpu_time.count() / 1000.0);

  // Test GPU kernel with two-stage approach
  printf("Testing GPU softmax v2 vec4 kernel (two-stage)...\n");
  constexpr int NUM_THREADS = 128 / 4;
  dim3 block(NUM_THREADS);
  dim3 grid((N / 4 + NUM_THREADS - 1) / NUM_THREADS);

  // Allocate temporary storage for exp values
  float *d_exp_vals;
  checkCudaErrors(cudaMalloc(&d_exp_vals, size));

  // Reset total for GPU computation
  checkCudaErrors(cudaMemset(d_total, 0, sizeof(float)));

  // Stage 1: Compute exp values and global sum
  softmax_v2_stage1<NUM_THREADS><<<grid, block>>>(d_x, d_exp_vals, d_total, N);
  checkCudaErrors(cudaGetLastError());
  checkCudaErrors(cudaDeviceSynchronize());

  // Get the total sum
  float h_total;
  checkCudaErrors(cudaMemcpy(&h_total, d_total, sizeof(float), cudaMemcpyDeviceToHost));

  // Stage 2: Normalize
  softmax_v2_stage2<NUM_THREADS><<<grid, block>>>(d_exp_vals, d_y, h_total, N);
  checkCudaErrors(cudaGetLastError());
  checkCudaErrors(cudaDeviceSynchronize());

  checkCudaErrors(cudaMemcpy(h_y_gpu, d_y, size, cudaMemcpyDeviceToHost));

  // Verify two-stage result
  bool passed_two_stage = verify_result(h_y_gpu, h_y_cpu, N);
  printf("Two-stage verification: %s\n", passed_two_stage ? "PASSED" : "FAILED");

  // Test single-kernel version for comparison
  printf("\nTesting single-kernel softmax v2 vec4 (legacy)...\n");

  // Allocate separate output for single-kernel test
  float *h_y_single = (float*)malloc(size);
  float *d_y_single, *d_total_single;
  checkCudaErrors(cudaMalloc(&d_y_single, size));
  checkCudaErrors(cudaMalloc(&d_total_single, sizeof(float)));

  // Reset total for single-kernel computation
  checkCudaErrors(cudaMemset(d_total_single, 0, sizeof(float)));

  // Run single-kernel version
  softmax_v2_vec4<NUM_THREADS><<<grid, block>>>(d_x, d_y_single, d_total_single, N);
  checkCudaErrors(cudaGetLastError());
  checkCudaErrors(cudaDeviceSynchronize());

  checkCudaErrors(cudaMemcpy(h_y_single, d_y_single, size, cudaMemcpyDeviceToHost));

  // Verify single-kernel result
  bool passed_single = verify_result(h_y_single, h_y_cpu, N);
  printf("Single-kernel (legacy) verification: %s\n", passed_single ? "PASSED" : "FAILED");

  // Test __threadfence() multi-block version
  printf("\nTesting __threadfence() multi-block softmax v2 vec4...\n");

  // Allocate separate output for threadfence test
  float *h_y_threadfence = (float*)malloc(size);
  float *d_y_threadfence, *d_total_threadfence;
  checkCudaErrors(cudaMalloc(&d_y_threadfence, size));
  checkCudaErrors(cudaMalloc(&d_total_threadfence, sizeof(float)));

  // Reset total for threadfence computation
  checkCudaErrors(cudaMemset(d_total_threadfence, 0, sizeof(float)));

  // Run threadfence version
  softmax_v2_multiblock<NUM_THREADS><<<grid, block>>>(d_x, d_y_threadfence, d_total_threadfence, N);
  checkCudaErrors(cudaGetLastError());
  checkCudaErrors(cudaDeviceSynchronize());

  checkCudaErrors(cudaMemcpy(h_y_threadfence, d_y_threadfence, size, cudaMemcpyDeviceToHost));

  // Verify threadfence result
  bool passed_threadfence = verify_result(h_y_threadfence, h_y_cpu, N);
  printf("__threadfence() verification: %s\n", passed_threadfence ? "PASSED" : "FAILED");

  bool passed = passed_two_stage && passed_single && passed_threadfence;

  if (passed) {
    printf("\nPerformance comparison:\n");

    // Benchmark two-stage version
    printf("Two-stage softmax v2 vec4 performance:\n");
    double time_two_stage = benchmark_kernel(
        [&](float *x, float *y, float *total, int n) {
          // Allocate temporary exp values for each benchmark run
          float *temp_exp;
          cudaMalloc(&temp_exp, size);
          cudaMemset(total, 0, sizeof(float));

          softmax_v2_stage1<NUM_THREADS><<<grid, block>>>(x, temp_exp, total, n);
          cudaDeviceSynchronize();

          float h_total_temp;
          cudaMemcpy(&h_total_temp, total, sizeof(float), cudaMemcpyDeviceToHost);

          softmax_v2_stage2<NUM_THREADS><<<grid, block>>>(temp_exp, y, h_total_temp, n);

          cudaFree(temp_exp);
        },
        d_x, d_y, d_total, N, 100); // Fewer iterations due to complexity

    double bandwidth_two_stage = (2.0 * size) / (time_two_stage * 1e-3) / 1e9;
    printf("  Time: %.3f ms, Bandwidth: %.2f GB/s\n", time_two_stage, bandwidth_two_stage);

    // Benchmark single-kernel version
    printf("Single-kernel (legacy) softmax v2 vec4 performance:\n");
    double time_single = benchmark_kernel(
        [&](float *x, float *y, float *total, int n) {
          cudaMemset(total, 0, sizeof(float));
          softmax_v2_vec4<NUM_THREADS><<<grid, block>>>(x, y, total, n);
        },
        d_x, d_y_single, d_total_single, N);

    double bandwidth_single = (2.0 * size) / (time_single * 1e-3) / 1e9;
    printf("  Time: %.3f ms, Bandwidth: %.2f GB/s\n", time_single, bandwidth_single);

    // Benchmark __threadfence() version
    printf("__threadfence() multi-block softmax v2 vec4 performance:\n");
    double time_threadfence = benchmark_kernel(
        [&](float *x, float *y, float *total, int n) {
          cudaMemset(total, 0, sizeof(float));
          softmax_v2_multiblock<NUM_THREADS><<<grid, block>>>(x, y, total, n);
        },
        d_x, d_y_threadfence, d_total_threadfence, N);

    double bandwidth_threadfence = (2.0 * size) / (time_threadfence * 1e-3) / 1e9;
    printf("  Time: %.3f ms, Bandwidth: %.2f GB/s\n", time_threadfence, bandwidth_threadfence);

    // Performance comparison
    printf("\nPerformance summary:\n");
    printf("  Legacy single-kernel is %.2fx %s than two-stage\n",
           time_two_stage / time_single,
           (time_single < time_two_stage) ? "faster" : "slower");
    printf("  __threadfence() is %.2fx %s than two-stage\n",
           time_two_stage / time_threadfence,
           (time_threadfence < time_two_stage) ? "faster" : "slower");
    printf("  __threadfence() is %.2fx %s than legacy\n",
           time_single / time_threadfence,
           (time_threadfence < time_single) ? "faster" : "slower");
  }

  // Cleanup
  free(h_y_single);
  free(h_y_threadfence);
  checkCudaErrors(cudaFree(d_y_single));
  checkCudaErrors(cudaFree(d_total_single));
  checkCudaErrors(cudaFree(d_y_threadfence));
  checkCudaErrors(cudaFree(d_total_threadfence));
  checkCudaErrors(cudaFree(d_exp_vals));

  printf("\n=================================\n");
  printf("Overall result: %s\n", passed ? "PASSED" : "FAILED");

  // Cleanup
  free(h_x);
  free(h_y_gpu);
  free(h_y_cpu);
  checkCudaErrors(cudaFree(d_x));
  checkCudaErrors(cudaFree(d_y));
  checkCudaErrors(cudaFree(d_total));

  return passed ? 0 : 1;
}