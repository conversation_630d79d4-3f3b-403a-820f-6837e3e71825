# 设置 CUDA 架构
set(CMAKE_CUDA_ARCHITECTURES 70 75 80 86)

# 编译选项
add_compile_options(-O2)
# set(CMAKE_CXX_STANDARD 17)
# set(CMAKE_CUDA_STANDARD 17)

# 查找 BLAS 库（这里假设 OpenBLAS 已安装）
find_package(BLAS REQUIRED)

# 源文件列表
set(UTIL_SOURCES
    copy_matrix.cpp
    compare_matrices.cpp
    random_matrix.cpp
    dclock.cpp
    REF_MMult.cpp
    print_matrix.cpp
)

set(TEST_SOURCES
    test_MMult.cpp
    MMult_cuda_3.cu
)

# 可执行文件
add_executable(test_MMult ${TEST_SOURCES} ${UTIL_SOURCES})

# 包含目录（关键修复：添加 CUDA 头文件路径）
target_include_directories(test_MMult PRIVATE
    ${CMAKE_CUDA_TOOLKIT_INCLUDE_DIRECTORIES}  # CUDA 头文件
    ${CUDAToolkit_INCLUDE_DIRS}
    ${BLAS_INCLUDE_DIRS}                       # 如果 BLAS 有自定义头文件路径
)

# 链接库（关键修复：添加 CUDA 运行时）
target_link_libraries(test_MMult PRIVATE
    m
    CUDA::cudart         # CUDA 运行时库
    CUDA::cublas         # cuBLAS 库
    ${BLAS_LIBRARIES}
    pthread
    gfortran
)

target_compile_options(test_MMult PRIVATE -lineinfo)

if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(test_MMult PRIVATE
        $<$<COMPILE_LANGUAGE:CUDA>:-G>
    )
endif()

# 安装规则（可选）
# install(TARGETS test_MMult DESTINATION bin)

# 自定义 clean 目标（CMake 自动生成，此处仅为说明）
# 使用 make clean 或 cmake --build build --target clean