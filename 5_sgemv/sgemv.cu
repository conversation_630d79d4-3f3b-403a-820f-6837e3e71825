#include <algorithm>
#include <cuda_bf16.h>
#include <cuda_fp16.h>
#include <cuda_fp8.h>
#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <float.h>
#include <stdio.h>
#include <stdlib.h>
#include <vector>
#include <chrono>
#include <cmath>

#define WARP_SIZE 32
#define INT4(value) (reinterpret_cast<int4 *>(&(value))[0])
#define FLOAT4(value) (reinterpret_cast<float4 *>(&(value))[0])

#define checkCudaErrors(func)                                                  \
  {                                                                            \
    cudaError_t e = (func);                                                    \
    if (e != cudaSuccess)                                                      \
      printf("%s %d CUDA: %s\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
  }

//  FP32
//  Warp Reduce Sum
template <const int kWarpSize = WARP_SIZE>
__device__ __forceinline__ float warp_reduce_sum_f32(float val) {
#pragma unroll
  for (int mask = kWarpSize >> 1; mask >= 1; mask >>= 1) {
    val += __shfl_xor_sync(0xffffffff, val, mask);
  }
  return val;
}

// SGEMV: Warp SGEMV K32
// 假设K为32的倍数，每个warp负责一行
// grid(M/4), block(32,4) blockDim.x=32=K, blockDim.y=4
// a: MxK, x: Kx1, y: Mx1, compute: y = a * x
__global__ void sgemv_k32_f32_kernel(float *a, float *x, float *y, int M,
                                     int K) {
  int tx = threadIdx.x;         // 0~31
  int ty = threadIdx.y;         // 0~4
  int bx = blockIdx.x;          // 0~M/4
  int lane = tx % WARP_SIZE;    // 0~31
  int m = bx * blockDim.y + ty; // (0~M/4) * 4 + (0~3)
  if (m < M) {
    float sum = 0.0f;
    int NUM_WARPS = (K + WARP_SIZE - 1) / WARP_SIZE;
#pragma unroll
    for (int w = 0; w < NUM_WARPS; ++w) {
      // 若NUM_WARPS>=2，先将当前行的数据累加到第一个warp中
      int k = w * WARP_SIZE + lane;
      sum += a[m * K + k] * x[k];
    }
    sum = warp_reduce_sum_f32<WARP_SIZE>(sum);
    if (lane == 0)
      y[m] = sum;
  }
}

// SGEMV: Warp SGEMV K128 + Vec4
// 假设K为128的倍数 float4
// grid(M/4), block(32,4) blockDim.x=32=K, blockDim.y=4
// a: MxK, x: Kx1, y: Mx1, compute: y = a * x
__global__ void sgemv_k128_f32x4_kernel(float *a, float *x, float *y, int M,
                                        int K) {
  // 每个线程负责4个元素，一个warp覆盖128个元素
  int tx = threadIdx.x;         // 0~31
  int ty = threadIdx.y;         // 0~3
  int bx = blockIdx.x;          // 0~M/4
  int lane = tx % WARP_SIZE;    // 0~31
  int m = blockDim.y * bx + ty; // (0~M/4) * 4 + (0~3)

  if (m < M) {
    float sum = 0.0f;
    // process 4*WARP_SIZE elements per warp.
    int NUM_WARPS = (((K + WARP_SIZE - 1) / WARP_SIZE) + 4 - 1) / 4;
#pragma unroll
    for (int w = 0; w < NUM_WARPS; ++w) {
      int k = (w * WARP_SIZE + lane) * 4;
      float4 reg_x = FLOAT4(x[k]);
      float4 reg_a = FLOAT4(a[m * K + k]);
      sum += (reg_a.x * reg_x.x + reg_a.y * reg_x.y + reg_a.z * reg_x.z +
              reg_a.w * reg_x.w);
    }
    sum = warp_reduce_sum_f32<WARP_SIZE>(sum);
    if (lane == 0)
      y[m] = sum;
  }
}

// SGEMV: Warp SGEMV K16
// 假设K为16 < 32,每个warp负责2行，每行有16个元素
// NUM_THREADS=128, NUM_WARPS=NUM_THREADS/WARP_SIZE;
// NUM_ROWS=NUM_WARPS * ROW_PER_WARP, grid(M/NUM_ROWS), block(32,NUM_WARPS)
// a: MxK, x: Kx1, y: Mx1, compute: y = a * x
template <const int ROW_PER_WARP = 2>
__global__ void sgemv_k16_f32_kernel(float *A, float *x, float *y, int M,
                                     int K) {
  constexpr int K_WARP_SIZE = (WARP_SIZE + ROW_PER_WARP - 1) / ROW_PER_WARP;
  int tx = threadIdx.x;      // 0~31
  int ty = threadIdx.y;      // 0~NUM_WARPS
  int bx = blockIdx.x;       // 0~M/NUM_ROWS (NUM_ROWS=NUM_WARPS * ROW_PER_WARP)
  int lane = tx % WARP_SIZE; // 0~31
  int k = lane % K_WARP_SIZE; // 0~15
  // gloabl row of a: MxK and y:Mx1, blockDim.y=NUM_WARPS
  int m = (blockDim.y * bx + ty) * ROW_PER_WARP + lane / K_WARP_SIZE;
  if (m < M) {
    float sum = A[m * K + k] * x[k];
    sum = warp_reduce_sum_f32<K_WARP_SIZE>(sum);
    // 注意是k == 0，而不是lane == 0
    if (k == 0)
      y[m] = sum;
  }
}

// CPU reference implementation for verification
void sgemv_cpu(float *A, float *x, float *y, int M, int K) {
  for (int m = 0; m < M; m++) {
    float sum = 0.0f;
    for (int k = 0; k < K; k++) {
      sum += A[m * K + k] * x[k];
    }
    y[m] = sum;
  }
}

// Utility function to initialize matrix and vector
void init_data(float *A, float *x, int M, int K) {
  // Initialize matrix A with random values
  for (int i = 0; i < M * K; i++) {
    A[i] = static_cast<float>(rand()) / RAND_MAX * 2.0f - 1.0f;
  }

  // Initialize vector x with random values
  for (int i = 0; i < K; i++) {
    x[i] = static_cast<float>(rand()) / RAND_MAX * 2.0f - 1.0f;
  }
}

// Verification function
bool verify_result(float *y_gpu, float *y_cpu, int M, float rel_tolerance = 1e-2f, float abs_tolerance = 1e-5f) {
  int failed_count = 0;
  float max_rel_err = 0.0f;
  float max_abs_err = 0.0f;

  for (int i = 0; i < M; i++) {
    float diff = std::abs(y_gpu[i] - y_cpu[i]);
    float abs_val = std::max(std::abs(y_cpu[i]), std::abs(y_gpu[i]));

    max_abs_err = std::max(max_abs_err, diff);

    float rel_err = (abs_val > 1e-8f) ? diff / abs_val : 0.0f;
    max_rel_err = std::max(max_rel_err, rel_err);

    // Pass if either absolute error or relative error is within tolerance
    bool passed = (diff <= abs_tolerance) || (rel_err <= rel_tolerance);

    if (!passed) {
      if (failed_count < 5) { // Only print first 5 failures
        printf("Verification failed at index %d: GPU=%.6f, CPU=%.6f, abs_err=%.6e, rel_err=%.6e\n",
               i, y_gpu[i], y_cpu[i], diff, rel_err);
      }
      failed_count++;
    }
  }

  printf("Max absolute error: %.6e, Max relative error: %.6e, Failed elements: %d/%d\n",
         max_abs_err, max_rel_err, failed_count, M);
  return failed_count == 0;
}

// Performance testing function template
template<typename KernelFunc>
double benchmark_kernel(KernelFunc kernel_func, float *d_A, float *d_x, float *d_y,
                       int M, int K, int num_iterations = 1000) {
  // Warm up
  for (int i = 0; i < 10; i++) {
    kernel_func(d_A, d_x, d_y, M, K);
  }
  checkCudaErrors(cudaDeviceSynchronize());

  // Timing
  auto start = std::chrono::high_resolution_clock::now();
  for (int i = 0; i < num_iterations; i++) {
    kernel_func(d_A, d_x, d_y, M, K);
  }
  checkCudaErrors(cudaDeviceSynchronize());
  auto end = std::chrono::high_resolution_clock::now();

  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
  return duration.count() / 1000.0 / num_iterations; // ms per iteration
}

int main(int argc, char **argv) {
  // Parse command line arguments
  int M = 4096, K = 32;
  if (argc >= 3) {
    M = atoi(argv[1]);
    K = atoi(argv[2]);
  }

  printf("SGEMV Performance Test\n");
  printf("Matrix size: M=%d, K=%d\n", M, K);
  printf("=================================\n");

  // Allocate host memory
  size_t size_A = M * K * sizeof(float);
  size_t size_x = K * sizeof(float);
  size_t size_y = M * sizeof(float);

  float *h_A = (float*)malloc(size_A);
  float *h_x = (float*)malloc(size_x);
  float *h_y_gpu = (float*)malloc(size_y);
  float *h_y_cpu = (float*)malloc(size_y);

  // Initialize data
  srand(42); // For reproducible results
  init_data(h_A, h_x, M, K);

  // Allocate device memory
  float *d_A, *d_x, *d_y;
  checkCudaErrors(cudaMalloc(&d_A, size_A));
  checkCudaErrors(cudaMalloc(&d_x, size_x));
  checkCudaErrors(cudaMalloc(&d_y, size_y));

  // Copy data to device
  checkCudaErrors(cudaMemcpy(d_A, h_A, size_A, cudaMemcpyHostToDevice));
  checkCudaErrors(cudaMemcpy(d_x, h_x, size_x, cudaMemcpyHostToDevice));

  // Compute CPU reference
  printf("Computing CPU reference...\n");
  auto cpu_start = std::chrono::high_resolution_clock::now();
  sgemv_cpu(h_A, h_x, h_y_cpu, M, K);
  auto cpu_end = std::chrono::high_resolution_clock::now();
  auto cpu_time = std::chrono::duration_cast<std::chrono::microseconds>(cpu_end - cpu_start);
  printf("CPU time: %.3f ms\n\n", cpu_time.count() / 1000.0);

  // Test different kernels based on K value
  bool all_passed = true;

  // Test 1: SGEMV K32 (works for any K that's multiple of 32)
  if (K % 32 == 0) {
    printf("Testing SGEMV K32 kernel...\n");
    dim3 grid((M + 3) / 4);
    dim3 block(32, 4);

    // Reset output
    checkCudaErrors(cudaMemset(d_y, 0, size_y));

    // Run kernel
    sgemv_k32_f32_kernel<<<grid, block>>>(d_A, d_x, d_y, M, K);
    checkCudaErrors(cudaGetLastError());
    checkCudaErrors(cudaDeviceSynchronize());

    // Copy result back and verify
    checkCudaErrors(cudaMemcpy(h_y_gpu, d_y, size_y, cudaMemcpyDeviceToHost));
    bool passed = verify_result(h_y_gpu, h_y_cpu, M);
    printf("K32 kernel: %s\n", passed ? "PASSED" : "FAILED");
    all_passed &= passed;

    // Benchmark
    double time_ms = benchmark_kernel(
        [](float *a, float *x, float *y, int M, int K) {
          dim3 grid((M + 3) / 4);
          dim3 block(32, 4);
          sgemv_k32_f32_kernel<<<grid, block>>>(a, x, y, M, K);
        },
        d_A, d_x, d_y, M, K);

    double gflops = (2.0 * M * K) / (time_ms * 1e-3) / 1e9;
    printf("K32 kernel performance: %.3f ms, %.2f GFLOPS\n\n", time_ms, gflops);
  }

  // Test 2: SGEMV K128 with float4 (works for K that's multiple of 128)
  if (K % 128 == 0) {
    printf("Testing SGEMV K128 float4 kernel...\n");
    dim3 grid((M + 3) / 4);
    dim3 block(32, 4);

    // Reset output
    checkCudaErrors(cudaMemset(d_y, 0, size_y));

    // Run kernel
    sgemv_k128_f32x4_kernel<<<grid, block>>>(d_A, d_x, d_y, M, K);
    checkCudaErrors(cudaGetLastError());
    checkCudaErrors(cudaDeviceSynchronize());

    // Copy result back and verify
    checkCudaErrors(cudaMemcpy(h_y_gpu, d_y, size_y, cudaMemcpyDeviceToHost));
    bool passed = verify_result(h_y_gpu, h_y_cpu, M);
    printf("K128 float4 kernel: %s\n", passed ? "PASSED" : "FAILED");
    all_passed &= passed;

    // Benchmark
    double time_ms = benchmark_kernel(
        [](float *a, float *x, float *y, int M, int K) {
          dim3 grid((M + 3) / 4);
          dim3 block(32, 4);
          sgemv_k128_f32x4_kernel<<<grid, block>>>(a, x, y, M, K);
        },
        d_A, d_x, d_y, M, K);

    double gflops = (2.0 * M * K) / (time_ms * 1e-3) / 1e9;
    printf("K128 float4 kernel performance: %.3f ms, %.2f GFLOPS\n\n", time_ms, gflops);
  }

  // Test 3: SGEMV K16 (works for K=16)
  if (K == 16) {
    printf("Testing SGEMV K16 kernel...\n");
    constexpr int ROW_PER_WARP = 2;
    constexpr int NUM_THREADS = 128;
    constexpr int NUM_WARPS = NUM_THREADS / WARP_SIZE;
    constexpr int NUM_ROWS = NUM_WARPS * ROW_PER_WARP;

    dim3 grid((M + NUM_ROWS - 1) / NUM_ROWS);
    dim3 block(32, NUM_WARPS);

    // Reset output
    checkCudaErrors(cudaMemset(d_y, 0, size_y));

    // Run kernel
    sgemv_k16_f32_kernel<ROW_PER_WARP><<<grid, block>>>(d_A, d_x, d_y, M, K);
    checkCudaErrors(cudaGetLastError());
    checkCudaErrors(cudaDeviceSynchronize());

    // Copy result back and verify
    checkCudaErrors(cudaMemcpy(h_y_gpu, d_y, size_y, cudaMemcpyDeviceToHost));
    bool passed = verify_result(h_y_gpu, h_y_cpu, M);
    printf("K16 kernel: %s\n", passed ? "PASSED" : "FAILED");
    all_passed &= passed;

    // Benchmark
    double time_ms = benchmark_kernel(
        [](float *a, float *x, float *y, int M, int K) {
          constexpr int ROW_PER_WARP = 2;
          constexpr int NUM_THREADS = 128;
          constexpr int NUM_WARPS = NUM_THREADS / WARP_SIZE;
          constexpr int NUM_ROWS = NUM_WARPS * ROW_PER_WARP;
          dim3 grid((M + NUM_ROWS - 1) / NUM_ROWS);
          dim3 block(32, NUM_WARPS);
          sgemv_k16_f32_kernel<ROW_PER_WARP><<<grid, block>>>(a, x, y, M, K);
        },
        d_A, d_x, d_y, M, K);

    double gflops = (2.0 * M * K) / (time_ms * 1e-3) / 1e9;
    printf("K16 kernel performance: %.3f ms, %.2f GFLOPS\n\n", time_ms, gflops);
  }

  // cuBLAS comparison
  printf("Testing cuBLAS SGEMV...\n");
  cublasHandle_t handle;
  cublasCreate(&handle);

  float alpha = 1.0f, beta = 0.0f;
  checkCudaErrors(cudaMemset(d_y, 0, size_y));

  // Warm up
  for (int i = 0; i < 10; i++) {
    cublasSgemv(handle, CUBLAS_OP_T, K, M, &alpha, d_A, K, d_x, 1, &beta, d_y, 1);
  }
  checkCudaErrors(cudaDeviceSynchronize());

  // Timing
  auto start = std::chrono::high_resolution_clock::now();
  for (int i = 0; i < 1000; i++) {
    cublasSgemv(handle, CUBLAS_OP_T, K, M, &alpha, d_A, K, d_x, 1, &beta, d_y, 1);
  }
  checkCudaErrors(cudaDeviceSynchronize());
  auto end = std::chrono::high_resolution_clock::now();

  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
  double cublas_time = duration.count() / 1000.0 / 1000.0; // ms per iteration
  double cublas_gflops = (2.0 * M * K) / (cublas_time * 1e-3) / 1e9;

  // Verify cuBLAS result
  checkCudaErrors(cudaMemcpy(h_y_gpu, d_y, size_y, cudaMemcpyDeviceToHost));
  bool cublas_passed = verify_result(h_y_gpu, h_y_cpu, M);
  printf("cuBLAS SGEMV: %s\n", cublas_passed ? "PASSED" : "FAILED");
  printf("cuBLAS performance: %.3f ms, %.2f GFLOPS\n\n", cublas_time, cublas_gflops);

  cublasDestroy(handle);

  // Summary
  printf("=================================\n");
  printf("Overall result: %s\n", all_passed ? "ALL TESTS PASSED" : "SOME TESTS FAILED");

  // Cleanup
  free(h_A);
  free(h_x);
  free(h_y_gpu);
  free(h_y_cpu);
  checkCudaErrors(cudaFree(d_A));
  checkCudaErrors(cudaFree(d_x));
  checkCudaErrors(cudaFree(d_y));

  return all_passed ? 0 : 1;
}