add_executable(sgemv_v0 sgemv_v0.cu)
target_link_libraries(sgemv_v0 PRIVATE CUDA::cudart CUDA::cublas)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sgemv_v0 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(sgemv_v0 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(sgemv_v1 sgemv_v1.cu)
target_link_libraries(sgemv_v1 PRIVATE CUDA::cudart CUDA::cublas)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sgemv_v1 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(sgemv_v1 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(sgemv_v2 sgemv_v2.cu)
target_link_libraries(sgemv_v2 PRIVATE CUDA::cudart CUDA::cublas)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sgemv_v2 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(sgemv_v2 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(sgemv sgemv.cu)
target_link_libraries(sgemv PRIVATE CUDA::cudart CUDA::cublas)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sgemv PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(sgemv PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(ComplexHalfGemv ComplexHalfGemv.cu)
target_link_libraries(ComplexHalfGemv PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(ComplexHalfGemv PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(ComplexHalfGemv PROPERTIES CUDA_SEPARABLE_COMPILATION ON)