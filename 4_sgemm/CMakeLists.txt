# 添加公共工具库
add_library(gemm_common STATIC
    common/REF_MMult.cpp
    common/copy_matrix.cpp
    common/compare_matrices.cpp
    common/random_matrix.cpp
    common/dclock.cpp
    common/cpu_sgemm.cpp
)

# 设置公共库的包含目录
target_include_directories(gemm_common PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/common
)

add_executable(sgemm_v0_naive sgemm_v0_naive.cu)
target_link_libraries(sgemm_v0_naive PRIVATE gemm_common CUDA::cudart CUDA::cublas)
# 设置主程序的包含目录
target_include_directories(sgemm_v0_naive PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/common
)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sgemm_v0_naive PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(sgemm_v0_naive PRIVATE -lineinfo)
endif()
set_target_properties(sgemm_v0_naive PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(sgemm_v1 sgemm_v1.cu)
target_link_libraries(sgemm_v1 PRIVATE gemm_common CUDA::cudart CUDA::cublas)
# 设置主程序的包含目录
target_include_directories(sgemm_v1 PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/common
)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sgemm_v1 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(sgemm_v1 PRIVATE -lineinfo)
endif()
set_target_properties(sgemm_v1 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(sgemm_v2 sgemm_v2.cu)
target_link_libraries(sgemm_v2 PRIVATE gemm_common CUDA::cudart CUDA::cublas)
# 设置主程序的包含目录
target_include_directories(sgemm_v2 PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/common
)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sgemm_v2 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(sgemm_v2 PRIVATE -lineinfo)
endif()
set_target_properties(sgemm_v2 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)


add_executable(sgemm_v3 sgemm_v3.cu)
target_link_libraries(sgemm_v3 PRIVATE gemm_common CUDA::cudart CUDA::cublas)
# 设置主程序的包含目录
target_include_directories(sgemm_v3 PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/common
)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sgemm_v3 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(sgemm_v3 PRIVATE -lineinfo)
endif()
set_target_properties(sgemm_v3 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(sgemm_v4 sgemm_v4.cu)
target_link_libraries(sgemm_v4 PRIVATE gemm_common CUDA::cudart CUDA::cublas)
# 设置主程序的包含目录
target_include_directories(sgemm_v4 PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/common
)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sgemm_v4 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
else()
    target_compile_options(sgemm_v4 PRIVATE -lineinfo)
endif()
set_target_properties(sgemm_v4 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)
# add_executable(sgemm_v1 sgemm_v1.cu)
# target_link_libraries(sgemm_v1 PRIVATE CUDA::cudart CUDA::cublas)
# if(CMAKE_BUILD_TYPE STREQUAL "Debug")
#     target_compile_options(sgemm_v1 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
# else()
#     target_compile_options(sgemm_v1 PRIVATE -lineinfo)
# endif()
# set_target_properties(sgemm_v1 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

# add_executable(sgemm_v3 sgemm_v3.cu)
# target_link_libraries(sgemm_v3 PRIVATE CUDA::cudart CUDA::cublas)
# if(CMAKE_BUILD_TYPE STREQUAL "Debug")
#     target_compile_options(sgemm_v3 PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
# endif()
# set_target_properties(sgemm_v3 PROPERTIES CUDA_SEPARABLE_COMPILATION ON)


# add_executable(nicolaswilde_cuda_sgemm nicolaswilde_cuda_sgemm.cu)
# target_link_libraries(nicolaswilde_cuda_sgemm PRIVATE CUDA::cudart CUDA::cublas)
# if(CMAKE_BUILD_TYPE STREQUAL "Debug")
#     target_compile_options(nicolaswilde_cuda_sgemm PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
# endif()
# set_target_properties(nicolaswilde_cuda_sgemm PROPERTIES CUDA_SEPARABLE_COMPILATION ON)