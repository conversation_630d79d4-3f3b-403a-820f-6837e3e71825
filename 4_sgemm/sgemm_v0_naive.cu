#include <stdio.h>
#include <stdlib.h>
#include "assert.h" 
#include <float.h>

// CUDA runtime
#include <cublas_v2.h>
#include <cuda_runtime.h>

// #include "common.h"
#include "helper.h"
#include "gemm_utils.h"

#define OFFSET(row, col, ld) ((row) * (ld) + (col))

__global__ void cuda_sgemm(float * __restrict__ a, float * __restrict__ b, float * __restrict__ c, const int M, const int N, const int K) {

    int n = blockIdx.x * blockDim.x + threadIdx.x;
    int m = blockIdx.y * blockDim.y + threadIdx.y;
    if (m < M && n < N) {
        float psum = 0.0;
        #pragma unroll
        for (int k = 0; k < K; k++) {
            psum += a[OFFSET(m, k, K)] * b[OFFSET(k, n, N)];
        }
        c[OFFSET(m, n, N)] = psum;
    }
}

__global__ void cuda_sgemm_v0(float* __restrict__ a, float* __restrict__ b, float* __restrict__ c, const int M, const int N, const int K){

    int n = blockIdx.x * blockDim.x + threadIdx.x;
    int m = blockIdx.y * blockDim.y + threadIdx.y;

    if(m < M && n < N){
        float psum = 0.0;
        #pragma unroll
        for(int k = 0;k < K; k++){
            psum += a[m * K + k] * b[k * N + n];
        }
        c[m * N + n] = psum;
    }
}

int main(int argc, char** argv) {
    int m = 512;
    int n = 512;
    int k = 512;

    const size_t mem_size_A = m * k * sizeof(float);
    const size_t mem_size_B = k * n * sizeof(float);
    const size_t mem_size_C = m * n * sizeof(float);

    float* matrix_A_host = (float *)malloc(mem_size_A);
    float* matrix_B_host = (float *)malloc(mem_size_B);

    float* matrix_C_host_gpu_calc = (float *)malloc(mem_size_C);
    float* matrix_C_host_cpu_calc = (float *)malloc(mem_size_C);

    random_matrix(m, k, matrix_A_host, m);
    random_matrix(k, n, matrix_B_host, k);
    memset(matrix_C_host_gpu_calc, 0, mem_size_C);
    memset(matrix_C_host_cpu_calc, 0, mem_size_C);

    float *matrix_A_device, *matrix_B_device, *matrix_C_device;
    checkCudaErrors(cudaMalloc((void **)&matrix_A_device, mem_size_A));
    checkCudaErrors(cudaMalloc((void **)&matrix_B_device, mem_size_B));
    checkCudaErrors(cudaMalloc((void **)&matrix_C_device, mem_size_C));

    checkCudaErrors(cudaMemcpy(matrix_A_device, matrix_A_host, mem_size_A, cudaMemcpyHostToDevice));
    checkCudaErrors(cudaMemcpy(matrix_B_device, matrix_B_host, mem_size_B, cudaMemcpyHostToDevice));

    cpu_sgemm(matrix_A_host, matrix_B_host, matrix_C_host_cpu_calc, m, n, k);

    constexpr int block_size = 16;
    dim3 block(block_size, block_size);
    dim3 grid((m + block_size - 1) / block_size, (n + block_size - 1) / block_size);
    cuda_sgemm<<<grid, block>>>(matrix_A_device, matrix_B_device, matrix_C_device, m, n, k);
    // cuda_sgemm_v0<<<grid, block>>>(matrix_A_device, matrix_B_device, matrix_C_device, m, n, k);

    checkCudaErrors(cudaMemcpy(matrix_C_host_gpu_calc, matrix_C_device, mem_size_C, cudaMemcpyDeviceToHost));

    float max_diff = compare_matrices(m, n, matrix_C_host_cpu_calc, m, matrix_C_host_gpu_calc, m);
    if (max_diff > 0.5f || max_diff < -0.5f) {
        printf("diff too big !\n");
        exit(-1);
    } else {
        printf("right\n");
    }

    checkCudaErrors(cudaFree(matrix_A_device));
    checkCudaErrors(cudaFree(matrix_B_device));
    checkCudaErrors(cudaFree(matrix_C_device));

    free(matrix_A_host);
    free(matrix_B_host);
    free(matrix_C_host_cpu_calc);
    free(matrix_C_host_gpu_calc);
    checkCudaErrors(cudaDeviceReset());

    return 0;
}