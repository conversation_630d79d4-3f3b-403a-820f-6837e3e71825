#include <stdio.h>
#include <stdlib.h>
#include "assert.h" 
#include <float.h>

// CUDA runtime
#include <cublas_v2.h>
#include <cuda_runtime.h>

// #include "common.h"
#include "helper.h"
#include "gemm_utils.h"

#define OFFSET(row, col, ld) ((row) * (ld) + (col))

template <const int BM = 32, const int BN = 32, const int BK = 32>
__global__ void cuda_sgemm(float * __restrict__ a, float * __restrict__ b, float * __restrict__ c, const int M, const int N, const int K) {
    __shared__ float s_a[BM][BK], s_b[BK][BN];

    int bx = blockIdx.x;
    int by = blockIdx.y;
    int tx = threadIdx.x;
    int ty = threadIdx.y;
    int tid = ty * blockDim.x + tx;

    int load_smem_a_m = tid / 32;
    int load_smem_a_k = tid % 32;
    int load_smem_b_k = tid / 32;
    int load_smem_b_n = tid % 32;

    int load_gmem_a_m = by * BM + load_smem_a_m;
    int load_gmem_b_n = bx * BN + load_smem_b_n;

    float sum = 0.f;
    for (int bk = 0; bk < (K + BK - 1)/ BK; ++bk){
        int load_gmem_a_k = bk * BK + load_smem_a_k;
        int load_gmem_a_addr = load_gmem_a_m * K + load_gmem_a_k;
        s_a[load_smem_a_m][load_smem_a_k] = a[load_gmem_a_addr];

        int load_gmem_b_k = bk * BK + load_smem_b_k;
        int load_gmem_b_addr = load_gmem_b_k * N + load_gmem_b_n;
        s_b[load_smem_b_k][load_smem_b_n] = b[load_gmem_b_addr];
        __syncthreads();
        
        #pragma unroll
        for (int k = 0; k < BK; ++k){
            int comp_smem_a_m = load_smem_a_m;
            int comp_smem_b_n = load_smem_b_n;
            sum += s_a[comp_smem_a_m][k] * s_b[k][comp_smem_b_n];
        }
        __syncthreads();
    }

    int store_gmem_c_m = load_gmem_a_m;
    int store_gmem_c_n = load_gmem_b_n;
    int store_gmem_c_addr = store_gmem_c_m * N + store_gmem_c_n;
    c[store_gmem_c_addr] = sum;
}

// template <const int BM = 32, const int BN = 32, const int BK = 32>
// __global__ void cuda_sgemm(float * __restrict__ a, float * __restrict__ b, float * __restrict__ c, const int M, const int N, const int K) {
//     __shared__ float s_a[BM][BK], s_b[BK][BN];

//     int bx = blockIdx.x;
//     int by = blockIdx.y;
//     int tx = threadIdx.x;
//     int ty = threadIdx.y;

//     int load_gmem_a_m = by * BM + ty;
//     int load_gmem_b_n = bx * BN + tx;

//     float sum = 0.f;
//     for (int bk = 0; bk < (K + BK - 1)/ BK; ++bk){
//         int load_gmem_a_k = bk * BK + tx;
//         int load_gmem_a_addr = load_gmem_a_m * K + load_gmem_a_k;
        
//         int load_gmem_b_k = bk * BK + ty;
//         int load_gmem_b_addr = load_gmem_b_k * N + load_gmem_b_n;
        
//         // 使用线程坐标直接搬运数据到共享内存
//         if(load_gmem_a_m < M && load_gmem_a_k < K)
//             s_a[ty][tx] = a[load_gmem_a_addr];
//         if(load_gmem_b_k < K && load_gmem_b_n < N)
//             s_b[ty][tx] = b[load_gmem_b_addr];
//         __syncthreads();
    
//         for (int k = 0; k < BK; ++k){
//             if(load_gmem_a_m < M && load_gmem_b_n < N)
//                 sum += s_a[ty][k] * s_b[k][tx];
//         }
//         __syncthreads();
//     }

//     if(load_gmem_a_m < M && load_gmem_b_n < N) {
//         int store_gmem_c_addr = load_gmem_a_m * N + load_gmem_b_n;
//         c[store_gmem_c_addr] = sum;
//     }
// }

template <const int BM=32, const int BN=32, const int BK=32>
__global__ void cuda_sgemm_v1(float* __restrict__ a, float* __restrict__ b, float* __restrict__ c, const int M, const int N,const int K) {
    __shared__ float s_a[BM][BK], s_b[BK][BN];

    int bx = blockIdx.x;
    int by = blockIdx.y;
    int tx = threadIdx.x;
    int ty = threadIdx.y;

    int load_gmem_a_m = by * BM + ty;
    int load_gmem_b_n = bx * BN + tx;
    
    float sum = 0.0f;
    for(int bk=0; bk<(K+BK-1)/BK; bk++){
        int load_gmem_a_k = bk * BK + tx;
        int load_gmem_a_addr = load_gmem_a_m * K + load_gmem_a_k;

        int load_gmem_b_k = bk * BK + ty;
        int load_gmem_b_addr = load_gmem_b_k * N + load_gmem_b_n;

        if(load_gmem_a_m < M && load_gmem_a_k < K) s_a[ty][tx] = a[load_gmem_a_addr];
        if(load_gmem_b_k < K && load_gmem_b_n < N) s_b[ty][tx] = b[load_gmem_b_addr];
        __syncthreads();

        for(int k=0; k<BK; k++){
            if(load_gmem_a_m < M && load_gmem_b_n < N){
                sum += s_a[ty][k] * s_b[k][tx];
            }
        }
        __syncthreads();
    }

    if(load_gmem_a_m < M && load_gmem_b_n < N){
        int store_gmem_c_addr = load_gmem_a_m * N + load_gmem_b_n;
        c[store_gmem_c_addr] = sum;
    }
}

int main(int argc, char** argv) {
    int m = 512;
    int n = 512;
    int k = 512;

    const size_t mem_size_A = m * k * sizeof(float);
    const size_t mem_size_B = k * n * sizeof(float);
    const size_t mem_size_C = m * n * sizeof(float);

    float* matrix_A_host = (float *)malloc(mem_size_A);
    float* matrix_B_host = (float *)malloc(mem_size_B);

    float* matrix_C_host_gpu_calc = (float *)malloc(mem_size_C);
    float* matrix_C_host_cpu_calc = (float *)malloc(mem_size_C);

    random_matrix(m, k, matrix_A_host, m);
    random_matrix(k, n, matrix_B_host, k);
    memset(matrix_C_host_gpu_calc, 0, mem_size_C);
    memset(matrix_C_host_cpu_calc, 0, mem_size_C);

    float *matrix_A_device, *matrix_B_device, *matrix_C_device;
    checkCudaErrors(cudaMalloc((void **)&matrix_A_device, mem_size_A));
    checkCudaErrors(cudaMalloc((void **)&matrix_B_device, mem_size_B));
    checkCudaErrors(cudaMalloc((void **)&matrix_C_device, mem_size_C));

    checkCudaErrors(cudaMemcpy(matrix_A_device, matrix_A_host, mem_size_A, cudaMemcpyHostToDevice));
    checkCudaErrors(cudaMemcpy(matrix_B_device, matrix_B_host, mem_size_B, cudaMemcpyHostToDevice));

    cpu_sgemm(matrix_A_host, matrix_B_host, matrix_C_host_cpu_calc, m, n, k);

    const int BM = 32;
    const int BN = 32;
    const int BK = 32;
    dim3 block(BN, BM);
    dim3 grid((n + BN - 1) / BN, (m + BM - 1) / BM);
    cuda_sgemm<BM, BN, BK><<<grid, block>>>(matrix_A_device, matrix_B_device, matrix_C_device, m, n, k);
    // cuda_sgemm_v1<BM, BN, BK><<<grid, block>>>(matrix_A_device, matrix_B_device, matrix_C_device, m, n, k);

    checkCudaErrors(cudaMemcpy(matrix_C_host_gpu_calc, matrix_C_device, mem_size_C, cudaMemcpyDeviceToHost));

    float max_diff = compare_matrices(m, n, matrix_C_host_cpu_calc, m, matrix_C_host_gpu_calc, m);
    if (max_diff > 0.5f || max_diff < -0.5f) {
        printf("diff too big !\n");
        exit(-1);
    } else {
        printf("right\n");
    }

    checkCudaErrors(cudaFree(matrix_A_device));
    checkCudaErrors(cudaFree(matrix_B_device));
    checkCudaErrors(cudaFree(matrix_C_device));

    free(matrix_A_host);
    free(matrix_B_host);
    free(matrix_C_host_cpu_calc);
    free(matrix_C_host_gpu_calc);
    checkCudaErrors(cudaDeviceReset());

    return 0;
}