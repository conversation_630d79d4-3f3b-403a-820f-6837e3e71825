#pragma once
// #include <cublas_v2.h>

double dclock();
void random_matrix(int, int, float *, int);
void copy_matrix(int, int, float *, int, float *, int);
float compare_matrices(int, int, float *, int, float *, int);
void REF_MMult(int, int, int, float *, int, float *, int, float *, int);
void cpu_sgemm(float *a, float *b, float *c, const int M, const int N, const int K);
// void MY_MMult(cublasHandle_t, int, int, int, float *, int, float *, int, float *, int);