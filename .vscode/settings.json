{
    "files.associations": {
        "makefile": "c",
        "array": "cpp",
        "deque": "cpp",
        "forward_list": "cpp",
        "list": "cpp",
        "string": "cpp",
        "vector": "cpp",
        "iterator": "cpp",
        "string_view": "cpp",
        "scoped_allocator": "cpp",
        "span": "cpp",
        "regex": "cpp",
        "__bit_reference": "cpp",
        "bitset": "cpp",
        "atomic": "cpp",
        "optional": "cpp",
        "ratio": "cpp",
        "system_error": "cpp",
        "functional": "cpp",
        "tuple": "cpp",
        "type_traits": "cpp",
        "utility": "cpp",
        "variant": "cpp",
        "algorithm": "cpp",
        "chrono": "cpp",
        "limits": "cpp",
        "random": "cpp",
        "any": "cpp",
        "barrier": "cpp",
        "bit": "cpp",
        "cctype": "cpp",
        "cfenv": "cpp",
        "charconv": "cpp",
        "cinttypes": "cpp",
        "clocale": "cpp",
        "cmath": "cpp",
        "codecvt": "cpp",
        "compare": "cpp",
        "complex": "cpp",
        "concepts": "cpp",
        "condition_variable": "cpp",
        "coroutine": "cpp",
        "csetjmp": "cpp",
        "csignal": "cpp",
        "cstdarg": "cpp",
        "cstddef": "cpp",
        "cstdint": "cpp",
        "cstdio": "cpp",
        "cstdlib": "cpp",
        "cstring": "cpp",
        "ctime": "cpp",
        "cuchar": "cpp",
        "cwchar": "cpp",
        "cwctype": "cpp",
        "map": "cpp",
        "set": "cpp",
        "unordered_map": "cpp",
        "unordered_set": "cpp",
        "exception": "cpp",
        "memory": "cpp",
        "memory_resource": "cpp",
        "numeric": "cpp",
        "source_location": "cpp",
        "fstream": "cpp",
        "future": "cpp",
        "initializer_list": "cpp",
        "iomanip": "cpp",
        "iosfwd": "cpp",
        "iostream": "cpp",
        "istream": "cpp",
        "latch": "cpp",
        "mutex": "cpp",
        "new": "cpp",
        "numbers": "cpp",
        "ostream": "cpp",
        "ranges": "cpp",
        "semaphore": "cpp",
        "shared_mutex": "cpp",
        "sstream": "cpp",
        "stdexcept": "cpp",
        "stop_token": "cpp",
        "streambuf": "cpp",
        "syncstream": "cpp",
        "thread": "cpp",
        "typeindex": "cpp",
        "typeinfo": "cpp",
        "valarray": "cpp"
    },
    "cmake.debugConfig": {
        "miDebuggerPath": "/usr/local/cuda/bin/cuda-gdb",
        "args": [
        ],
    },
}