// {
//     "version": "0.2.0",
//     "configurations": [
//         {
//             "name": "CUDA C++: Launch",
//             "type": "cuda-gdb",
//             "request": "launch",
//             // "program": "${workspaceFolder}/build/1_cuda_test/testdebug",
//             // "program": "${workspaceFolder}/build/2_elementwise/elementwise_debug",
//             // "program": "${workspaceFolder}/build/3_reduce/reduce",
//             // "program": "${workspaceFolder}/build/1_cuda_test/sumMatrix",
//             // "program": "${workspaceFolder}/build/1_cuda_test/helloFromGPU",
//             "program": "${workspaceFolder}/build/4_sgemm/sgemm_v1",
//             "args": ["8", "8", "8"]
//         }
//     ]
// }


// {
//     "version": "0.2.0",
//     "configurations": [
//         {
//             "name": "(cuda-gdb) CUDA Full Debug",
//             // "type": "cppdbg",
//             "type": "cuda-gdb",
//             "request": "launch",
//             "program": "${workspaceFolder}/build/4_sgemm/sgemm_v1",
//             // "program": "${workspaceFolder}/build/3_reduce/reduce_v7_shuffle",
//             // "program": "${workspaceFolder}/build/9_optimize_gemm/test_MMult",
//             "args": ["8", "8", "8"],
//             "stopAtEntry": false,
//             "cwd": "${workspaceFolder}",
//             "environment": [],
//             "externalConsole": false,
//             "MIMode": "gdb",
//             // "miDebuggerPath": "/usr/bin/gdb",
//             "miDebuggerPath": "/usr/local/cuda/bin/cuda-gdb",
//             "setupCommands": [
//                 {
//                     "description": "Enable pretty-printing",
//                     "text": "-enable-pretty-printing",
//                     "ignoreFailures": true
//                 }
//             ],
//             "preLaunchTask": "cmake_build_debug"
//         }
//     ]
// }


{
    "configurations": [
        {
            "name": "CUDA C++: Launch",
            "type": "cuda-gdb",
            "request": "launch",
            // "program": "${workspaceFolder}/build/9_optimize_gemm/test_MMult",
            // "program": "${workspaceFolder}/build/3_reduce/block_all_reduce_v1",
            "program": "${workspaceFolder}/build/4_sgemm/sgemm_v3",
            // "program": "${workspaceFolder}/build/2_elementwise/elementwise_debug",
            // "program": "${workspaceFolder}/build/4_sgemm/sgemm_v0_naive",
            // "program": "${workspaceFolder}/build/10_hgemm/hgemm_mma_v1_m16n8k16_naive",
            // "program": "${workspaceFolder}/build/10_hgemm/hgemm_wmma_m16n16k16_mma4x2_warp2x4",
            // "program": "${workspaceFolder}/build/15_dot-product/dot_product_v1",
            // "program": "${workspaceFolder}/build/16_softmax/softmax_v0",
            // "args": ["4096 4096 4096"],
        }
    ]
}