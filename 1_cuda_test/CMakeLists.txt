add_executable(testdebug hello.cu)
target_link_libraries(testdebug PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(testdebug PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(testdebug PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

add_executable(sumMatrix sumMatrix.cu)
target_link_libraries(sumMatrix PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(sumMatrix PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(sumMatrix PROPERTIES CUDA_SEPARABLE_COMPILATION ON)


add_executable(helloFromGPU helloFromGPU.cu)
target_link_libraries(helloFromGPU PRIVATE CUDA::cudart)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(helloFromGPU PRIVATE $<$<COMPILE_LANGUAGE:CUDA>:-G>)
endif()
set_target_properties(helloFromGPU PROPERTIES CUDA_SEPARABLE_COMPILATION ON)